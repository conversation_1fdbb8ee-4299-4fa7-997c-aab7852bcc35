"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAsyncHandler = exports.asyncHandler = void 0;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
const createAsyncHandler = (handler) => {
    return (0, exports.asyncHandler)(handler);
};
exports.createAsyncHandler = createAsyncHandler;
exports.default = exports.asyncHandler;
//# sourceMappingURL=asyncHandler.js.map