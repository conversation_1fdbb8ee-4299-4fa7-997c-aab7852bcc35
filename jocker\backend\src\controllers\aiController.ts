import { Request, Response } from 'express';
import { generateArticles, generateArticleContent, generatePeerReview } from '../services/aiService';
import { ApiResponse, AIGenerateArticlesRequest, AIGenerateContentRequest } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import { GoogleGenAI } from '@google/genai';

/**
 * AI 生成文章列表
 */
export const generateArticleList = asyncHandler(async (req: Request, res: Response) => {
  const request: AIGenerateArticlesRequest = {
    count: parseInt(req.body.count) || 5,
    theme: req.body.theme,
  };

  const articles = await generateArticles(request);

  const response: ApiResponse = {
    success: true,
    message: `成功生成 ${articles.length} 篇文章`,
    data: articles,
  };

  res.status(201).json(response);
});

/**
 * AI 生成文章内容
 */
export const generateContent = asyncHandler(async (req: Request, res: Response) => {
  const articleId = parseInt(req.params.id);

  if (isNaN(articleId)) {
    return res.status(400).json({
      success: false,
      message: '无效的文章 ID',
    });
  }

  const request: AIGenerateContentRequest = { articleId };
  const content = await generateArticleContent(request);

  const response: ApiResponse = {
    success: true,
    message: '生成文章内容成功',
    data: { content },
  };

  res.status(200).json(response);
});

/**
 * AI 生成同行评议
 */
export const generateReview = asyncHandler(async (req: Request, res: Response) => {
  const { author, title, abstract } = req.body;

  if (!author || !title || !abstract) {
    return res.status(400).json({
      success: false,
      message: '缺少必要参数：author, title, abstract',
    });
  }

  const review = await generatePeerReview(author, title, abstract);

  const response: ApiResponse = {
    success: true,
    message: '生成同行评议成功',
    data: { review },
  };

  res.status(200).json(response);
});

/**
 * 测试 Google AI API 连接
 */
export const testGoogleConnection = asyncHandler(async (req: Request, res: Response) => {
  try {
    const ai = new GoogleGenAI({
      apiKey: process.env.GOOGLE_AI_API_KEY || "AIzaSyCu5MqxX5CW2ZvYtWqr9san9ZyI5cE3kLY"
    });

    // 发送一个简单的测试请求
    const testResponse = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: '请回复"连接成功"',
    });

    const response: ApiResponse = {
      success: true,
      message: 'Google AI API 连接成功',
      data: {
        testResult: testResponse.text || '收到响应但内容为空',
        timestamp: new Date().toISOString()
      },
    };

    res.status(200).json(response);

  } catch (error) {
    console.error('Google AI API 连接测试失败:', error);

    const response: ApiResponse = {
      success: false,
      message: 'Google AI API 连接失败',
      data: {
        error: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date().toISOString()
      },
    };

    res.status(500).json(response);
  }
});
