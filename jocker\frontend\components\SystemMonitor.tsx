import React, { useState, useEffect } from 'react';
import { systemApi } from '../src/services/api';

interface SystemStats {
  disk: {
    total: string;
    used: string;
    available: string;
    usagePercent: string;
  };
  application: {
    totalSize: string;
    components: Array<{ name: string; size: string }>;
  };
  audio: {
    totalSize: string;
    fileCount: number;
  };
  database: {
    size: string;
    articles: number;
    users: number;
    audioFiles: number;
  };
  memory: {
    total: string;
    used: string;
    free: string;
  };
  cpu: {
    usage: string;
  };
  uptime: string;
}

export const SystemMonitor: React.FC = () => {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [storageAnalysis, setStorageAnalysis] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'storage' | 'cleanup'>('overview');
  const [cleanupLoading, setCleanupLoading] = useState(false);

  useEffect(() => {
    loadSystemStats();
  }, []);

  const loadSystemStats = async () => {
    try {
      setLoading(true);
      const [statsData, storageData] = await Promise.all([
        systemApi.getSystemStats(),
        systemApi.getStorageAnalysis()
      ]);
      setStats(statsData);
      setStorageAnalysis(storageData);
    } catch (error) {
      console.error('加载系统统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCleanup = async () => {
    try {
      setCleanupLoading(true);
      await systemApi.cleanupStorage();
      alert('存储清理完成！');
      loadSystemStats(); // 重新加载统计信息
    } catch (error) {
      console.error('存储清理失败:', error);
      alert('存储清理失败');
    } finally {
      setCleanupLoading(false);
    }
  };

  const getUsageColor = (percentage: string) => {
    const num = parseInt(percentage);
    if (num < 50) return 'bg-green-500';
    if (num < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
            <div className="h-3 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-red-600">无法加载系统统计信息</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* 标签页导航 */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { key: 'overview', label: '📊 系统概览' },
            { key: 'storage', label: '💾 存储分析' },
            { key: 'cleanup', label: '🧹 清理工具' }
          ].map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* 磁盘使用情况 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3">💽 磁盘使用情况</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{stats.disk.total}</div>
                  <div className="text-sm text-gray-600">总容量</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{stats.disk.used}</div>
                  <div className="text-sm text-gray-600">已使用</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{stats.disk.available}</div>
                  <div className="text-sm text-gray-600">可用空间</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{stats.disk.usagePercent}</div>
                  <div className="text-sm text-gray-600">使用率</div>
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className={`h-3 rounded-full ${getUsageColor(stats.disk.usagePercent)}`}
                  style={{ width: stats.disk.usagePercent }}
                ></div>
              </div>
            </div>

            {/* 应用程序使用情况 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3">🚀 应用程序占用</h3>
              <div className="mb-4">
                <div className="text-xl font-bold text-purple-600">总大小: {stats.application.totalSize}</div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {stats.application.components.map((component, index) => (
                  <div key={index} className="bg-white rounded p-3 border">
                    <div className="font-medium">{component.name}</div>
                    <div className="text-sm text-gray-600">{component.size}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* 音频文件统计 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3">🎵 音频文件统计</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{stats.audio.fileCount}</div>
                  <div className="text-sm text-gray-600">音频文件数量</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{stats.audio.totalSize}</div>
                  <div className="text-sm text-gray-600">音频文件总大小</div>
                </div>
              </div>
            </div>

            {/* 数据库统计 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3">🗄️ 数据库统计</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-xl font-bold text-blue-600">{stats.database.articles}</div>
                  <div className="text-sm text-gray-600">文章数量</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-green-600">{stats.database.users}</div>
                  <div className="text-sm text-gray-600">用户数量</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-purple-600">{stats.database.audioFiles}</div>
                  <div className="text-sm text-gray-600">音频文件</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-orange-600">{stats.database.size}</div>
                  <div className="text-sm text-gray-600">数据库大小</div>
                </div>
              </div>
            </div>

            {/* 系统资源 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">🧠 内存使用</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>总内存:</span>
                    <span className="font-medium">{stats.memory.total}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>已使用:</span>
                    <span className="font-medium text-orange-600">{stats.memory.used}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>可用:</span>
                    <span className="font-medium text-green-600">{stats.memory.free}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">⚡ 系统状态</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>CPU使用率:</span>
                    <span className="font-medium">{stats.cpu.usage}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>系统运行时间:</span>
                    <span className="font-medium">{stats.uptime}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'storage' && storageAnalysis && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">📁 详细存储分析</h3>
            
            {/* 音频文件分析 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold mb-3">🎵 音频文件详情</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-xl font-bold">{storageAnalysis.audioFiles.totalFiles}</div>
                  <div className="text-sm text-gray-600">总文件数</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold">{storageAnalysis.audioFiles.totalSize}</div>
                  <div className="text-sm text-gray-600">总大小</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold">{storageAnalysis.audioFiles.averageSize}</div>
                  <div className="text-sm text-gray-600">平均大小</div>
                </div>
              </div>
              
              {storageAnalysis.audioFiles.largestFiles.length > 0 && (
                <div>
                  <h5 className="font-medium mb-2">最大的音频文件:</h5>
                  <div className="space-y-2">
                    {storageAnalysis.audioFiles.largestFiles.map((file: any, index: number) => (
                      <div key={index} className="bg-white rounded p-3 border">
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="font-medium">{file.articleTitle}</div>
                            <div className="text-sm text-gray-600">{file.fileName}</div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">{file.size}</div>
                            <div className="text-sm text-gray-600">
                              {new Date(file.uploadDate).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'cleanup' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">🧹 存储清理工具</h3>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">注意</h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>清理操作将删除临时文件和缓存，这个操作不可逆。请确保您了解这些操作的影响。</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white border rounded-lg p-4">
              <h4 className="font-semibold mb-3">可清理的内容:</h4>
              <ul className="space-y-2 mb-4">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  7天前的日志文件
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  npm缓存文件
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  临时文件
                </li>
              </ul>
              
              <button
                onClick={handleCleanup}
                disabled={cleanupLoading}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {cleanupLoading ? '清理中...' : '开始清理'}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 刷新按钮 */}
      <div className="border-t border-gray-200 px-6 py-3">
        <button
          onClick={loadSystemStats}
          disabled={loading}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {loading ? '刷新中...' : '刷新数据'}
        </button>
      </div>
    </div>
  );
};
