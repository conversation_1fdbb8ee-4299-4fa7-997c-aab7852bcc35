{"version": "v2.3.0", "type": "minor", "title": "系统监控与动态日志管理", "description": "新增完整的服务器资源监控功能和动态日志管理系统，提升系统可观测性和维护效率", "changes": ["📊 系统监控功能：实时显示磁盘、内存、CPU使用情况和应用程序占用空间", "💾 存储分析工具：详细分析音频文件、图片、数据库等各类文件的存储占用", "🧹 存储清理功能：一键清理临时文件、日志文件和npm缓存，释放存储空间", "📋 动态日志管理：完全替代静态日志，支持在线添加、编辑、删除系统更新记录", "🕒 实时时间戳：日志时间自动获取服务器当前时间，不再停留在过去", "🗄️ 数据库驱动：所有日志存储在数据库中，支持分页、筛选、搜索等功能", "👤 用户追踪：记录日志创建者信息，便于团队协作和责任追溯", "📥 历史日志迁移：一键导入所有历史版本记录，无缝过渡到新系统", "🎨 美观界面：专业的时间线展示和响应式设计，提升用户体验", "🔧 权限控制：只有管理员可以管理日志，确保系统记录的准确性"]}