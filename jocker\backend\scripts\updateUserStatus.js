const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateUserStatus() {
  try {
    console.log('开始更新用户状态...');
    
    // 为所有没有状态的用户设置默认状态
    const result = await prisma.user.updateMany({
      where: {
        OR: [
          { status: null },
          { status: '' }
        ]
      },
      data: {
        status: 'ACTIVE'
      }
    });
    
    console.log(`✅ 已更新 ${result.count} 个用户的状态为 ACTIVE`);
    
    // 显示所有用户的状态
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        status: true,
        role: true
      }
    });
    
    console.log('\n当前用户状态:');
    users.forEach(user => {
      console.log(`- ${user.username} (${user.email}): ${user.status || 'NULL'} [${user.role}]`);
    });
    
  } catch (error) {
    console.error('更新用户状态失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateUserStatus();
