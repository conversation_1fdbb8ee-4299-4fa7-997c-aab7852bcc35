import React, { useState, useEffect } from 'react';

// 图片数据类型
export interface Figure {
  id: string;
  figureNumber: number;
  title: string;
  description?: string;
  caption: string;
  imageUrl?: string;
  thumbnailUrl?: string;
  width?: number;
  height?: number;
  status: 'pending' | 'generating' | 'completed' | 'failed';
  errorMsg?: string;
}

// 图片预览模态框
interface ImagePreviewModalProps {
  figure: Figure;
  isOpen: boolean;
  onClose: () => void;
}

const ImagePreviewModal: React.FC<ImagePreviewModalProps> = ({ figure, isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="relative max-w-4xl max-h-full bg-white rounded-lg overflow-hidden">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-colors"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* 图片内容 */}
        <div className="p-6">
          {figure.imageUrl ? (
            <img
              src={figure.imageUrl}
              alt={figure.caption}
              className="w-full h-auto max-h-[70vh] object-contain rounded-lg"
            />
          ) : (
            <div className="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-gray-500">图片加载中...</span>
            </div>
          )}
          
          {/* 图片信息 */}
          <div className="mt-4">
            <h3 className="text-lg font-semibold text-gray-900">{figure.title}</h3>
            <p className="text-sm text-gray-600 mt-2">{figure.caption}</p>
            {figure.description && (
              <p className="text-xs text-gray-500 mt-2">{figure.description}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// 单个图片组件
interface FigureComponentProps {
  figure: Figure;
  onImageClick?: (figure: Figure) => void;
  showAdmin?: boolean;
  onRegenerate?: (figureId: string) => void;
}

export const FigureComponent: React.FC<FigureComponentProps> = ({ 
  figure, 
  onImageClick,
  showAdmin = false,
  onRegenerate
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  const handleImageClick = () => {
    if (figure.imageUrl && onImageClick) {
      onImageClick(figure);
    }
  };

  const renderImageContent = () => {
    switch (figure.status) {
      case 'pending':
        return (
          <div className="w-full h-64 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center">
            <svg className="w-12 h-12 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span className="text-gray-500 text-sm">等待生成</span>
          </div>
        );

      case 'generating':
        return (
          <div className="w-full h-64 bg-blue-50 border border-blue-200 rounded-lg flex flex-col items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
            <span className="text-blue-600 text-sm">AI 正在生成图片...</span>
          </div>
        );

      case 'failed':
        return (
          <div className="w-full h-64 bg-red-50 border border-red-200 rounded-lg flex flex-col items-center justify-center">
            <svg className="w-12 h-12 text-red-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-red-600 text-sm">生成失败</span>
            {figure.errorMsg && (
              <span className="text-red-500 text-xs mt-1 text-center px-2">{figure.errorMsg}</span>
            )}
            {showAdmin && onRegenerate && (
              <button
                onClick={() => onRegenerate(figure.id)}
                className="mt-2 px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors"
              >
                重新生成
              </button>
            )}
          </div>
        );

      case 'completed':
        if (!figure.imageUrl) {
          return (
            <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center">
              <span className="text-gray-500 text-sm">图片 URL 缺失</span>
            </div>
          );
        }

        return (
          <div className="relative group">
            <img
              src={figure.imageUrl}
              alt={figure.caption}
              className={`w-full h-auto rounded-lg shadow-md cursor-pointer transition-all duration-300 hover:shadow-lg ${
                imageLoaded ? 'opacity-100' : 'opacity-0'
              }`}
              onLoad={handleImageLoad}
              onError={handleImageError}
              onClick={handleImageClick}
            />
            
            {!imageLoaded && !imageError && (
              <div className="absolute inset-0 bg-gray-200 rounded-lg flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-600"></div>
              </div>
            )}

            {imageError && (
              <div className="absolute inset-0 bg-gray-100 rounded-lg flex flex-col items-center justify-center">
                <span className="text-gray-500 text-sm">图片加载失败</span>
                {showAdmin && onRegenerate && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      if (confirm('图片加载失败，确定要重新生成吗？')) {
                        onRegenerate(figure.id);
                      }
                    }}
                    className="mt-2 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                  >
                    重新生成
                  </button>
                )}
              </div>
            )}

            {/* 悬停时显示放大图标 */}
            {imageLoaded && !imageError && (
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-lg flex items-center justify-center">
                <svg className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                </svg>
              </div>
            )}

            {/* 管理员操作按钮 */}
            {showAdmin && onRegenerate && imageLoaded && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onRegenerate(figure.id);
                }}
                className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-opacity-75"
                title="重新生成图片"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            )}
          </div>
        );

      default:
        return (
          <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center">
            <span className="text-gray-500 text-sm">未知状态</span>
          </div>
        );
    }
  };

  return (
    <figure className="my-8">
      {/* 图片内容 */}
      {renderImageContent()}
      
      {/* 图注 */}
      <figcaption className="mt-3 text-sm text-gray-600 text-center italic leading-relaxed">
        {figure.caption}
      </figcaption>
    </figure>
  );
};

// 图片列表组件
interface FigureListProps {
  figures: Figure[];
  showAdmin?: boolean;
  onRegenerate?: (figureId: string) => void;
}

export const FigureList: React.FC<FigureListProps> = ({ 
  figures, 
  showAdmin = false, 
  onRegenerate 
}) => {
  const [previewFigure, setPreviewFigure] = useState<Figure | null>(null);

  const handleImageClick = (figure: Figure) => {
    setPreviewFigure(figure);
  };

  const handleClosePreview = () => {
    setPreviewFigure(null);
  };

  if (figures.length === 0) {
    return null;
  }

  return (
    <>
      <div className="space-y-8">
        {figures.map((figure) => (
          <FigureComponent
            key={figure.id}
            figure={figure}
            onImageClick={handleImageClick}
            showAdmin={showAdmin}
            onRegenerate={onRegenerate}
          />
        ))}
      </div>

      {/* 图片预览模态框 */}
      {previewFigure && (
        <ImagePreviewModal
          figure={previewFigure}
          isOpen={true}
          onClose={handleClosePreview}
        />
      )}
    </>
  );
};
