# 🚀 Jocker 项目部署指南

本项目提供了多种一键部署脚本，支持前端、后端的快速部署。

## 📋 部署脚本说明

### 🐧 Linux/macOS 脚本

| 脚本文件 | 功能 | 使用方法 |
|---------|------|----------|
| `deploy-all.sh` | 一键全量部署 | `./deploy-all.sh` |
| `deploy-frontend.sh` | 仅部署前端 | `./deploy-frontend.sh` |
| `deploy-backend.sh` | 仅部署后端 | `./deploy-backend.sh` |

### 🪟 Windows 脚本

| 脚本文件 | 功能 | 使用方法 |
|---------|------|----------|
| `deploy.bat` | 一键部署 | `deploy.bat [frontend\|backend\|all]` |

## 🛠️ 使用方法

### Linux/macOS 环境

#### 1. 全量部署（推荐）
```bash
# 赋予执行权限
chmod +x deploy-all.sh

# 部署前端和后端
./deploy-all.sh

# 仅部署前端
./deploy-all.sh --frontend-only

# 仅部署后端
./deploy-all.sh --backend-only
```

#### 2. 单独部署
```bash
# 仅部署前端
chmod +x deploy-frontend.sh
./deploy-frontend.sh

# 仅部署后端
chmod +x deploy-backend.sh
./deploy-backend.sh
```

### Windows 环境

#### 1. 全量部署
```cmd
# 部署前端和后端
deploy.bat all

# 或者直接运行
deploy.bat
```

#### 2. 单独部署
```cmd
# 仅部署前端
deploy.bat frontend

# 仅部署后端
deploy.bat backend
```

## 📦 部署流程

### 前端部署流程
1. 🔧 安装依赖 (`npm install`)
2. 🏗️ 构建项目 (`npm run build`)
3. 📤 上传到服务器 (`scp dist/* -> /var/www/jocker/jocker/frontend/dist/`)

### 后端部署流程
1. 🔧 安装依赖 (`npm install`)
2. 🗄️ 生成Prisma客户端 (`npx prisma generate`)
3. 🏗️ 构建项目 (`npm run build`)
4. 📤 上传文件到服务器
   - `dist/` 目录
   - `package.json`
   - `package-lock.json`
   - `prisma/` 目录
5. 🔄 服务器操作
   - 安装生产依赖
   - 生成Prisma客户端
   - 重启PM2服务 (`pm2 restart jocker-backend`)

## 🌐 部署地址

- **前端地址**: http://47.79.90.239
- **后端API**: http://47.79.90.239:5003

## ⚙️ 服务器配置

### PM2 服务
- **服务名称**: `jocker-backend`
- **启动文件**: `dist/index.js`
- **工作目录**: `/var/www/jocker/jocker/backend/`

### 文件路径
- **前端部署路径**: `/var/www/jocker/jocker/frontend/dist/`
- **后端部署路径**: `/var/www/jocker/jocker/backend/`

## 🔧 故障排除

### 常见问题

#### 1. SSH连接失败
```bash
# 检查SSH密钥配置
ssh-add -l

# 测试SSH连接
ssh root@47.79.90.239
```

#### 2. 构建失败
```bash
# 清理node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 3. PM2服务异常
```bash
# 连接服务器检查PM2状态
ssh root@47.79.90.239
pm2 status
pm2 logs jocker-backend
```

#### 4. 权限问题
```bash
# 赋予脚本执行权限
chmod +x *.sh
```

### 手动部署步骤

如果自动部署失败，可以按照以下步骤手动部署：

#### 前端手动部署
```bash
cd frontend
npm install
npm run build
scp -r dist/* root@47.79.90.239:/var/www/jocker/jocker/frontend/dist/
```

#### 后端手动部署
```bash
cd backend
npm install
npx prisma generate
npm run build
scp -r dist package.json package-lock.json prisma root@47.79.90.239:/var/www/jocker/jocker/backend/
ssh root@47.79.90.239 "cd /var/www/jocker/jocker/backend && npm install --production && npx prisma generate && pm2 restart jocker-backend"
```

## 📝 注意事项

1. **确保SSH密钥配置正确**，能够无密码连接到服务器
2. **在项目根目录运行脚本**，确保路径正确
3. **网络连接稳定**，避免上传过程中断
4. **服务器空间充足**，确保有足够的磁盘空间
5. **备份重要数据**，部署前建议备份数据库

## 🎯 快速开始

对于新用户，推荐使用以下命令快速部署：

```bash
# Linux/macOS
chmod +x deploy-all.sh && ./deploy-all.sh

# Windows
deploy.bat
```

部署完成后访问 http://47.79.90.239 查看网站！
