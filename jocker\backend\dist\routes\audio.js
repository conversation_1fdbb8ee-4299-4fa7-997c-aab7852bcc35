"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const audioController_1 = require("../controllers/audioController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const handleAudioUploadError = (error, req, res, next) => {
    console.error('🎵 音频上传错误:', error);
    if (error.code === 'LIMIT_FILE_SIZE') {
        return res.status(413).json({
            success: false,
            message: '文件太大，请选择小于50MB的音频文件',
        });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
        return res.status(400).json({
            success: false,
            message: '不支持的文件类型，请上传音频文件',
        });
    }
    if (error.message === '只允许上传音频文件') {
        return res.status(400).json({
            success: false,
            message: '只允许上传音频文件',
        });
    }
    return res.status(500).json({
        success: false,
        message: '上传失败，请重试',
    });
};
router.post('/articles/:articleId', auth_1.authenticateToken, auth_1.requireEditor, (req, res, next) => {
    audioController_1.audioUpload.single('audio')(req, res, (error) => {
        if (error) {
            return handleAudioUploadError(error, req, res, next);
        }
        next();
    });
}, audioController_1.uploadArticleAudio);
router.get('/articles/:articleId', audioController_1.getArticleAudio);
router.put('/articles/:articleId', auth_1.authenticateToken, auth_1.requireEditor, audioController_1.updateArticleAudio);
router.delete('/articles/:articleId', auth_1.authenticateToken, auth_1.requireEditor, audioController_1.deleteArticleAudio);
exports.default = router;
//# sourceMappingURL=audio.js.map