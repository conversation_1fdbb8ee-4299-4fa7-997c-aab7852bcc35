import { Article, AIGenerateArticlesRequest, AIGenerateContentRequest } from '../types';
export declare const generateArticles: (request: AIGenerateArticlesRequest) => Promise<Article[]>;
export declare const generateArticleContent: (request: AIGenerateContentRequest) => Promise<string>;
export declare const generatePeerReview: (author: string, title: string, abstract: string) => Promise<string>;
export declare const generateArticleContentWithFigures: (article: any) => Promise<string>;
//# sourceMappingURL=aiService.d.ts.map