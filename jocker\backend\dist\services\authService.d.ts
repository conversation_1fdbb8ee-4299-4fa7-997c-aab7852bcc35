import { LoginRequest, RegisterRequest, AuthResponse } from '../types';
export declare const registerUser: (userData: RegisterRequest) => Promise<AuthResponse>;
export declare const loginUser: (loginData: LoginRequest) => Promise<AuthResponse>;
export declare const getUserById: (userId: number) => Promise<{
    id: number;
    email: string;
    username: string;
    name: string;
    avatar: string;
    role: string;
    createdAt: Date;
    updatedAt: Date;
}>;
//# sourceMappingURL=authService.d.ts.map