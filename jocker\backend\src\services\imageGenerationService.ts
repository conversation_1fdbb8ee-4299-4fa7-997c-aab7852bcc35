import { GoogleGenAI } from '@google/genai';
import prisma from '../config/database';
import { updateFigureStatus } from './figureService';

/**
 * 图片生成配置
 */
interface ImageGenerationConfig {
  width: number;
  height: number;
  aspectRatio: string;
  style: string;
}

/**
 * 默认图片生成配置（适合学术期刊）
 */
const DEFAULT_CONFIG: ImageGenerationConfig = {
  width: 1024,
  height: 1024,
  aspectRatio: '1:1',
  style: 'photographic', // 适合学术风格的真实感图片
};

/**
 * 使用 Gemini Imagen 生成图片
 */
export const generateImageWithGemini = async (
  prompt: string,
  config: ImageGenerationConfig = DEFAULT_CONFIG
): Promise<{ imageUrl: string; revisedPrompt?: string }> => {
  const apiKey = process.env.GOOGLE_AI_API_KEY;

  if (!apiKey) {
    throw new Error('Google AI API Key 未配置');
  }

  try {
    const ai = new GoogleGenAI({ apiKey });

    // 为学术图片优化提示词
    const academicPrompt = `${prompt}. Professional academic illustration style, clean and clear, suitable for scientific publication, high quality, detailed, professional composition.`;

    // 使用 Gemini 的图片生成功能（与封面生成相同的方式）
    const imageResponse = await ai.models.generateImages({
      model: 'imagen-3.0-generate-002',
      prompt: academicPrompt,
      config: {
        numberOfImages: 1,
        outputMimeType: 'image/jpeg',
        aspectRatio: config.aspectRatio,
      },
    });

    const base64ImageBytes = imageResponse.generatedImages?.[0]?.image?.imageBytes || '';

    if (!base64ImageBytes) {
      throw new Error('Gemini 未返回图片数据');
    }

    // 生成 base64 格式的图片 URL（与封面生成相同）
    const imageUrl = `data:image/jpeg;base64,${base64ImageBytes}`;

    return {
      imageUrl,
      revisedPrompt: academicPrompt,
    };

  } catch (error) {
    console.error('Gemini 图片生成失败:', error);
    throw error;
  }
};

/**
 * 使用 Stable Diffusion (备选方案)
 * 简化版本，返回占位符图片
 */
export const generateImageWithStableDiffusion = async (
  prompt: string
): Promise<{ imageUrl: string }> => {
  console.log('🔄 使用 Stable Diffusion 备选方案生成图片');

  try {
    // 生成一个学术风格的占位符图片 URL
    // 实际部署时可以替换为真实的 Stable Diffusion API 调用
    const timestamp = Date.now();
    const mockImageUrl = `https://via.placeholder.com/1024x1024/f0f0f0/666666?text=Academic+Figure+${timestamp}`;

    // 模拟 API 调用延迟
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log(`✅ Stable Diffusion 占位符图片生成完成: ${mockImageUrl}`);

    return { imageUrl: mockImageUrl };

  } catch (error) {
    console.error('Stable Diffusion 图片生成失败:', error);
    throw error;
  }
};

// 移除了 uploadImageToStorage 函数，因为我们使用 base64 内嵌图片

/**
 * 为文章生成所有图片
 */
export const generateAllFiguresForArticle = async (articleId: number): Promise<void> => {
  try {
    // 获取文章的所有待生成图片
    const figures = await prisma.figure.findMany({
      where: {
        articleId,
        status: 'pending',
      },
      orderBy: { figureNumber: 'asc' },
    });

    console.log(`🎨 开始为文章 ${articleId} 生成 ${figures.length} 个图片`);

    for (const figure of figures) {
      try {
        // 更新状态为生成中
        await updateFigureStatus(figure.id, 'generating');

        // 生成图片（优先使用 Gemini，失败时使用 Stable Diffusion）
        let imageResult;
        try {
          imageResult = await generateImageWithGemini(figure.imagePrompt);
        } catch (geminiError) {
          console.warn(`Gemini 生成失败，尝试 Stable Diffusion: ${geminiError}`);
          imageResult = await generateImageWithStableDiffusion(figure.imagePrompt);
        }

        // 更新图片记录
        await updateFigureStatus(figure.id, 'completed', imageResult.imageUrl);

        // 记录生成日志
        await prisma.aIGenerationLog.create({
          data: {
            type: 'figure',
            prompt: figure.imagePrompt,
            response: imageResult.imageUrl,
            success: true,
            relatedId: figure.id,
          },
        });

        console.log(`✅ 图片 ${figure.figureNumber} 生成成功: ${imageResult.imageUrl}`);

      } catch (error) {
        console.error(`❌ 图片 ${figure.figureNumber} 生成失败:`, error);

        // 更新状态为失败
        const errorMsg = error instanceof Error ? error.message : '未知错误';
        await updateFigureStatus(figure.id, 'failed', undefined, errorMsg);

        // 记录错误日志
        await prisma.aIGenerationLog.create({
          data: {
            type: 'figure',
            prompt: figure.imagePrompt,
            success: false,
            errorMsg,
            relatedId: figure.id,
          },
        });
      }
    }

    console.log(`🎉 文章 ${articleId} 的图片生成任务完成`);

  } catch (error) {
    console.error('批量生成图片失败:', error);
    throw error;
  }
};

/**
 * 重新生成单个图片
 */
export const regenerateFigure = async (figureId: string): Promise<void> => {
  const figure = await prisma.figure.findUnique({
    where: { id: figureId },
  });

  if (!figure) {
    throw new Error('图片记录不存在');
  }

  try {
    await updateFigureStatus(figureId, 'generating');

    let imageResult;
    try {
      imageResult = await generateImageWithGemini(figure.imagePrompt);
    } catch (geminiError) {
      imageResult = await generateImageWithStableDiffusion(figure.imagePrompt);
    }

    await updateFigureStatus(figureId, 'completed', imageResult.imageUrl);

    console.log(`✅ 图片 ${figure.figureNumber} 重新生成成功`);

  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : '未知错误';
    await updateFigureStatus(figureId, 'failed', undefined, errorMsg);
    throw error;
  }
};
