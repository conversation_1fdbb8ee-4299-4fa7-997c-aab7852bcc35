import React from 'react';

interface EnhancedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'gradient';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  fullWidth?: boolean;
  animated?: boolean;
}

export const EnhancedButton: React.FC<EnhancedButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  className = '',
  type = 'button',
  fullWidth = false,
  animated = true
}) => {
  const baseClasses = `
    inline-flex items-center justify-center font-medium rounded-lg
    transition-all duration-300 ease-in-out
    focus:outline-none focus:ring-4 focus:ring-opacity-50
    disabled:opacity-50 disabled:cursor-not-allowed
    ${animated ? 'transform hover:scale-105 active:scale-95' : ''}
    ${fullWidth ? 'w-full' : ''}
  `;

  const variantClasses = {
    primary: `
      bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800
      text-white shadow-lg hover:shadow-xl
      focus:ring-blue-300
      ${animated ? 'hover:shadow-blue-500/25' : ''}
    `,
    secondary: `
      bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800
      text-white shadow-lg hover:shadow-xl
      focus:ring-gray-300
      ${animated ? 'hover:shadow-gray-500/25' : ''}
    `,
    success: `
      bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800
      text-white shadow-lg hover:shadow-xl
      focus:ring-green-300
      ${animated ? 'hover:shadow-green-500/25' : ''}
    `,
    warning: `
      bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600
      text-white shadow-lg hover:shadow-xl
      focus:ring-yellow-300
      ${animated ? 'hover:shadow-yellow-500/25' : ''}
    `,
    danger: `
      bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800
      text-white shadow-lg hover:shadow-xl
      focus:ring-red-300
      ${animated ? 'hover:shadow-red-500/25' : ''}
    `,
    gradient: `
      bg-gradient-to-r from-purple-600 via-blue-600 to-green-600
      hover:from-purple-700 hover:via-blue-700 hover:to-green-700
      text-white shadow-lg hover:shadow-xl
      focus:ring-purple-300
      ${animated ? 'hover:shadow-purple-500/25' : ''}
      bg-size-200 bg-pos-0 hover:bg-pos-100
    `
  };

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2.5 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg'
  };

  const LoadingSpinner = () => (
    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" fill="none" viewBox="0 0 24 24">
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  );

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${className}
      `}
    >
      {loading && <LoadingSpinner />}
      
      {!loading && icon && iconPosition === 'left' && (
        <span className="mr-2">{icon}</span>
      )}
      
      <span className={loading ? 'opacity-75' : ''}>
        {children}
      </span>
      
      {!loading && icon && iconPosition === 'right' && (
        <span className="ml-2">{icon}</span>
      )}
    </button>
  );
};

// 预设的图标按钮组件
export const IconButton: React.FC<{
  icon: React.ReactNode;
  onClick?: () => void;
  variant?: EnhancedButtonProps['variant'];
  size?: EnhancedButtonProps['size'];
  className?: string;
  title?: string;
  disabled?: boolean;
}> = ({ icon, onClick, variant = 'secondary', size = 'md', className = '', title, disabled }) => {
  const sizeClasses = {
    sm: 'p-2',
    md: 'p-2.5',
    lg: 'p-3',
    xl: 'p-4'
  };

  return (
    <EnhancedButton
      onClick={onClick}
      variant={variant}
      size={size}
      disabled={disabled}
      className={`${sizeClasses[size]} ${className}`}
      title={title}
      animated={true}
    >
      {icon}
    </EnhancedButton>
  );
};

// 浮动操作按钮
export const FloatingActionButton: React.FC<{
  icon: React.ReactNode;
  onClick?: () => void;
  className?: string;
  title?: string;
}> = ({ icon, onClick, className = '', title }) => {
  return (
    <button
      onClick={onClick}
      title={title}
      className={`
        fixed bottom-6 right-6 z-50
        w-14 h-14 bg-gradient-to-r from-blue-600 to-purple-600
        text-white rounded-full shadow-lg hover:shadow-xl
        flex items-center justify-center
        transform hover:scale-110 active:scale-95
        transition-all duration-300 ease-in-out
        focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-50
        ${className}
      `}
    >
      {icon}
    </button>
  );
};

// 按钮组组件
export const ButtonGroup: React.FC<{
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
}> = ({ children, className = '', orientation = 'horizontal' }) => {
  return (
    <div className={`
      inline-flex ${orientation === 'vertical' ? 'flex-col' : 'flex-row'}
      rounded-lg shadow-sm overflow-hidden
      ${className}
    `}>
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, {
            className: `
              ${child.props.className || ''}
              ${orientation === 'horizontal' ? 'rounded-none' : 'rounded-none'}
              ${index === 0 ? (orientation === 'horizontal' ? 'rounded-l-lg' : 'rounded-t-lg') : ''}
              ${index === React.Children.count(children) - 1 ? (orientation === 'horizontal' ? 'rounded-r-lg' : 'rounded-b-lg') : ''}
              ${orientation === 'horizontal' && index > 0 ? 'border-l border-white/20' : ''}
              ${orientation === 'vertical' && index > 0 ? 'border-t border-white/20' : ''}
            `
          });
        }
        return child;
      })}
    </div>
  );
};
