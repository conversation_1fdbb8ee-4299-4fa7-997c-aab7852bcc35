@echo off
echo ========================================
echo 🧹 Server Cleanup Script
echo ========================================
echo.

echo Current server storage usage:
ssh root@************ "du -sh /var/www/jocker/jocker/backend/*"

echo.
echo Checking audio files...
ssh root@************ "ls -la /var/www/jocker/jocker/backend/uploads/audio/ | head -5"

echo.
echo Total audio files:
ssh root@************ "ls /var/www/jocker/jocker/backend/uploads/audio/ | wc -l"

echo.
echo ========================================
echo Cleanup Options:
echo ========================================
echo 1. Remove all audio files (saves ~265M)
echo 2. Remove old backup files
echo 3. Clean node_modules and reinstall
echo 4. Remove duplicate database files
echo 5. Full cleanup (all above)
echo 0. Cancel
echo.

set /p choice="Enter your choice (0-5): "

if "%choice%"=="1" goto cleanup_audio
if "%choice%"=="2" goto cleanup_backups
if "%choice%"=="3" goto cleanup_node_modules
if "%choice%"=="4" goto cleanup_databases
if "%choice%"=="5" goto full_cleanup
if "%choice%"=="0" goto cancel
goto invalid

:cleanup_audio
echo Removing audio files...
ssh root@************ "rm -rf /var/www/jocker/jocker/backend/uploads/audio/*"
echo Audio files removed!
goto end

:cleanup_backups
echo Removing backup files...
ssh root@************ "rm -f /var/www/jocker/jocker/backend/*.tar.gz"
ssh root@************ "rm -f /var/www/jocker/jocker/backend/prisma/database.db"
echo Backup files removed!
goto end

:cleanup_node_modules
echo Cleaning node_modules...
ssh root@************ "cd /var/www/jocker/jocker/backend && rm -rf node_modules && npm install --production"
echo Node modules cleaned and reinstalled!
goto end

:cleanup_databases
echo Removing duplicate database files...
ssh root@************ "rm -f /var/www/jocker/jocker/backend/prisma/database.db"
ssh root@************ "rm -f /var/www/jocker/jocker/backend/prisma/y"
echo Duplicate database files removed!
goto end

:full_cleanup
echo Performing full cleanup...
ssh root@************ "rm -rf /var/www/jocker/jocker/backend/uploads/audio/*"
ssh root@************ "rm -f /var/www/jocker/jocker/backend/*.tar.gz"
ssh root@************ "rm -f /var/www/jocker/jocker/backend/prisma/database.db"
ssh root@************ "rm -f /var/www/jocker/jocker/backend/prisma/y"
echo Full cleanup completed!
goto end

:invalid
echo Invalid choice!
goto end

:cancel
echo Cleanup cancelled.
goto end

:end
echo.
echo Final storage usage:
ssh root@************ "du -sh /var/www/jocker/jocker/backend"
echo.
pause
