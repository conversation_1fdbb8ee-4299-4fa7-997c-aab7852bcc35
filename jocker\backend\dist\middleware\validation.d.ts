import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
export declare const validate: (schema: Joi.ObjectSchema) => (req: Request, res: Response, next: NextFunction) => void;
export declare const registerSchema: Joi.ObjectSchema<any>;
export declare const loginSchema: Joi.ObjectSchema<any>;
export declare const createArticleSchema: Joi.ObjectSchema<any>;
export declare const updateArticleSchema: Joi.ObjectSchema<any>;
export declare const aiGenerateArticlesSchema: Joi.ObjectSchema<any>;
export declare const aiGenerateContentSchema: Joi.ObjectSchema<any>;
//# sourceMappingURL=validation.d.ts.map