import React, { useState, useEffect } from 'react';
import { logApi } from '../src/services/api';

interface SystemLog {
  id: string;
  version: string;
  date: string;
  type: 'major' | 'minor' | 'patch' | 'hotfix';
  title?: string;
  description?: string;
  changes: string[];
  creator?: {
    id: number;
    username: string;
    name?: string;
  };
}

export const LogManagement: React.FC = () => {
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingLog, setEditingLog] = useState<SystemLog | null>(null);
  const [importing, setImporting] = useState(false);
  const [formData, setFormData] = useState({
    version: '',
    type: 'minor' as 'major' | 'minor' | 'patch' | 'hotfix',
    title: '',
    description: '',
    changes: ['']
  });

  useEffect(() => {
    loadLogs();
  }, []);

  const loadLogs = async () => {
    try {
      setLoading(true);
      const data = await logApi.getSystemLogs({ limit: 50 });
      setLogs(data.logs);
    } catch (error) {
      console.error('加载日志失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const cleanChanges = formData.changes.filter(change => change.trim());
    if (cleanChanges.length === 0) {
      alert('请至少添加一条变更记录');
      return;
    }

    try {
      const submitData = {
        ...formData,
        changes: cleanChanges
      };

      if (editingLog) {
        await logApi.updateSystemLog(editingLog.id, submitData);
      } else {
        await logApi.createSystemLog(submitData);
      }

      setShowCreateForm(false);
      setEditingLog(null);
      setFormData({
        version: '',
        type: 'minor',
        title: '',
        description: '',
        changes: ['']
      });
      loadLogs();
    } catch (error) {
      console.error('保存日志失败:', error);
      alert('保存失败，请检查版本号是否重复');
    }
  };

  const handleEdit = (log: SystemLog) => {
    setEditingLog(log);
    setFormData({
      version: log.version,
      type: log.type,
      title: log.title || '',
      description: log.description || '',
      changes: log.changes.length > 0 ? log.changes : ['']
    });
    setShowCreateForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这条日志吗？')) return;
    
    try {
      await logApi.deleteSystemLog(id);
      loadLogs();
    } catch (error) {
      console.error('删除日志失败:', error);
      alert('删除失败');
    }
  };

  const addChangeField = () => {
    setFormData(prev => ({
      ...prev,
      changes: [...prev.changes, '']
    }));
  };

  const updateChange = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      changes: prev.changes.map((change, i) => i === index ? value : change)
    }));
  };

  const removeChange = (index: number) => {
    setFormData(prev => ({
      ...prev,
      changes: prev.changes.filter((_, i) => i !== index)
    }));
  };

  const handleImportHistory = async () => {
    if (!confirm('确定要导入历史日志吗？这将添加所有历史版本记录。')) return;

    try {
      setImporting(true);

      // 完整的历史日志数据
      const historyLogs = [
        {
          version: "v2.2.0",
          date: "2025-01-11",
          type: "minor",
          title: "播客功能上线",
          description: "为Joker期刊系统添加完整的音频播客功能，提供多媒体阅读体验",
          changes: [
            "🎵 播客功能：支持为文章添加音频播客，提供完整的多媒体阅读体验",
            "🎧 音频播放器：专业的音频播放控件，支持进度控制、音量调节、时长显示",
            "📤 音频上传：管理员可上传最大50MB的音频文件，支持拖拽上传",
            "🎛️ 播放控制：播放/暂停、进度跳转、音量控制等完整功能",
            "📊 音频管理：支持音频信息编辑、删除等管理功能",
            "🔒 权限控制：只有管理员和编辑可以上传/删除音频文件",
            "📱 响应式设计：音频播放器适配各种屏幕尺寸"
          ]
        },
        {
          version: "v2.1.3",
          date: "2025-01-11",
          type: "patch",
          title: "AI模型升级",
          description: "全面升级到Gemini 2.5 Flash模型，提升生成性能",
          changes: [
            "🤖 AI模型升级：全面切换到Gemini 2.5 Flash模型，提升生成速度和质量",
            "⚡ 性能优化：新模型响应更快，生成内容更加稳定",
            "🔧 统一配置：前后端AI调用统一使用最新的模型版本",
            "📝 改进生成：文章内容和图片生成都使用优化后的模型"
          ]
        },
        {
          version: "v2.1.2",
          date: "2025-01-11",
          type: "patch",
          title: "用户去重统计",
          description: "优化观看次数统计，避免重复计数",
          changes: [
            "👤 用户去重统计：同一用户只会被统计一次观看次数，避免重复刷新增加浏览量",
            "🔍 智能识别：基于用户ID（已登录）+ IP地址 + 浏览器指纹进行用户识别",
            "💾 本地缓存：使用localStorage记录已访问文章，减少不必要的API调用",
            "📊 真实统计：新增观看记录表，提供更准确的用户行为分析",
            "🎯 精确去重：确保统计数据的真实性和准确性"
          ]
        },
        {
          version: "v2.1.1",
          date: "2025-01-11",
          type: "patch",
          title: "修复观看次数统计",
          description: "解决观看次数不更新的问题",
          changes: [
            "🔧 修复观看次数统计：解决前端未调用后端API导致观看次数不更新的问题",
            "📊 优化统计逻辑：移除重复的观看次数增加逻辑，确保统计准确性",
            "⚡ 实时更新：进入文章页面时自动获取最新的观看次数并显示",
            "🎯 精确计算：每次访问文章详情页都会正确增加观看次数"
          ]
        },
        {
          version: "v2.1.0",
          date: "2025-01-11",
          type: "minor",
          title: "观看统计与批量管理",
          description: "新增观看次数统计和批量删除功能",
          changes: [
            "📊 新增观看次数统计：文章详情页自动统计观看次数，实时显示浏览量",
            "🗂️ 批量删除功能：管理员可多选文章进行批量删除，提升管理效率",
            "✏️ 观看次数编辑：管理员可手动修改文章观看次数，支持数据调整",
            "☑️ 全选功能：支持一键全选/取消全选所有文章，便于批量操作",
            "🎯 智能选择：选中文章数量实时显示，操作状态清晰可见",
            "🔒 权限控制：批量删除和观看次数编辑仅限管理员和编辑使用",
            "📈 数据展示：文章列表和详情页都显示观看次数，数据透明化"
          ]
        },
        {
          version: "v2.0.1",
          date: "2025-01-11",
          type: "patch",
          title: "UI优化与修复",
          description: "修复重复显示问题，优化用户体验",
          changes: [
            "🔧 修复UI重复问题：移除重复的'文章内容尚未生成'提示，优化用户体验",
            "📝 修复日志显示：解决更新日志中重复列表符号的显示问题",
            "🎯 优化内容生成：改进prompt避免重复生成标题和摘要，从正文部分开始延续",
            "✨ 智能内容衔接：生成详细内容时会基于已有的标题、作者、摘要信息继续写作",
            "🎨 界面清理：移除冗余的提示信息，让界面更加简洁明了"
          ]
        },
        {
          version: "v2.0.0",
          date: "2025-01-11",
          type: "major",
          title: "Joker 2.0 重大更新",
          description: "期刊正式更名为Joker，新增双模式系统",
          changes: [
            "🃏 重大更名：期刊名称从'Jocker'正式更名为'Joker'，修正了所有前端显示",
            "🎭 双模式系统：支持小丑模式（戏谑文章）和严肃模式（专业学术文章）",
            "🎓 学术生产力：严肃模式生成符合顶级期刊标准的专业文章，成为真正的学术工具",
            "🎪 娱乐保留：小丑模式继续提供幽默、戏谑的学术文章",
            "⚙️ 智能切换：根据模式自动使用不同的AI提示词模板",
            "🔬 严谨标准：严肃模式确保科学方法论、统计分析、引用格式的正确性",
            "🎨 全新界面：生成弹窗提供直观的模式选择体验",
            "📚 完整更新：DOI、引用格式、PDF文件名等全面更新为新期刊名称",
            "🔧 技术优化：修复了多个渲染和显示问题，提升系统稳定性"
          ]
        },
        {
          version: "v1.8.0",
          date: "2025-01-11",
          type: "minor",
          title: "生成模式选择",
          description: "新增小丑模式和严肃模式选择",
          changes: [
            "🎭 新增生成模式选择：支持小丑模式（戏谑文章）和严肃模式（专业学术文章）",
            "🎓 严肃模式功能：生成符合顶级期刊发表标准的专业学术文章，可作为真正的学术生产力工具",
            "🎪 小丑模式保留：继续支持原有的戏谑、幽默学术文章生成",
            "⚙️ 智能提示词切换：根据选择的模式自动使用不同的AI提示词模板",
            "🔬 严谨学术标准：严肃模式确保科学方法论正确、统计分析合理、引用格式标准",
            "🎨 直观模式选择：生成和重新生成弹窗中提供清晰的模式选择界面"
          ]
        },
        {
          version: "v1.7.6",
          date: "2025-01-11",
          type: "patch",
          title: "图片生成优化",
          description: "修复图片生成问题，优化错误处理",
          changes: [
            "🔧 修复图片生成问题：限制AI提示词长度在400字符以内，避免413错误",
            "⚡ 优化错误处理：图片生成失败时保存提示词并跳过到下一张，避免进度卡死",
            "🎨 修复后台布局：解决文章标题过长时编辑删除按钮被挤出容器的问题",
            "📱 改进响应式设计：后台文章管理界面按钮现在垂直排列，确保始终可点击",
            "🖼️ 修复失败图片显示：失败的图片现在会在Figures画廊中显示，可以点击重新生成",
            "💡 改进用户提示：文章中的失败占位符会提示用户到Figures画廊重新生成",
            "📝 修复列表渲染问题：解决有序列表和无序列表在Markdown渲染时丢失的问题",
            "🔧 优化Markdown解析：改进marked配置和列表格式处理，确保列表正确显示"
          ]
        },
        {
          version: "v1.7.5",
          date: "2025-01-11",
          type: "minor",
          title: "文章编辑功能",
          description: "新增文章编辑功能和权限管理",
          changes: [
            "✏️ 新增文章编辑功能：管理员和编辑可以直接编辑文章的Markdown内容",
            "🎯 权限分级管理：管理员可生成和重新生成文章，编辑只能编辑现有内容",
            "📝 实时编辑界面：提供专业的Markdown编辑器，支持语法提示和格式指导",
            "🔄 自动重新渲染：编辑保存后自动重新渲染文章内容，包括图片和引用",
            "🛡️ 角色权限控制：基于用户角色显示不同的操作按钮和功能",
            "💾 即时保存功能：编辑内容可即时保存到数据库并更新显示"
          ]
        },
        {
          version: "v1.7.4",
          date: "2025-01-11",
          type: "patch",
          title: "文献引用优化",
          description: "优化文献引用格式和PDF导出",
          changes: [
            "📚 文献引用上标化：文献引用现在显示为学术期刊标准的上标格式，保留中括号",
            "🔗 引用点击跳转：点击文献引用可平滑滚动到对应参考文献并高亮显示",
            "📄 优化PDF导出：移除PDF中多余的'Peer Reviewed'和分类标签行",
            "🔧 修复加粗渲染：改进紧贴数字和Figure标题的加粗标记处理",
            "✨ 引用格式支持：支持[1], [1,2], [1-3], [1,3-5]等多种引用格式",
            "🎨 视觉优化：引用点击后临时高亮效果，提升用户体验"
          ]
        },
        {
          version: "v1.7.3",
          date: "2025-01-11",
          type: "minor",
          title: "文章生成流程优化",
          description: "分离基本信息生成和详细内容生成",
          changes: [
            "🎯 优化文章生成流程：分离基本信息生成和详细内容生成",
            "📝 首页生成文章：现在只需输入主题，生成标题、作者、摘要和封面",
            "⚙️ 详情页生成正文：在文章详情页可自定义字数、图片数量和参考文献数量",
            "🎨 新增生成参数弹窗：用户可在生成正文时设置文章长度、图片数量和参考文献数量",
            "📚 新增参考文献数量控制：支持自定义1-10篇参考文献，默认3-5篇",
            "🔄 改进重新生成功能：支持自定义参数重新生成文章内容",
            "📄 优化PDF导出：文件名现在使用DOI格式",
            "📊 新增生成进度显示：实时显示文章生成进度"
          ]
        }
      ];

      await logApi.importHistoryLogs(historyLogs);
      alert('历史日志导入成功！');
      loadLogs();
    } catch (error) {
      console.error('导入历史日志失败:', error);
      alert('导入失败，请检查网络连接');
    } finally {
      setImporting(false);
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'major': return 'bg-red-100 text-red-800';
      case 'minor': return 'bg-blue-100 text-blue-800';
      case 'patch': return 'bg-green-100 text-green-800';
      case 'hotfix': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">📋 日志管理</h2>
        <div className="flex space-x-3">
          <button
            onClick={handleImportHistory}
            disabled={importing}
            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:bg-gray-400"
          >
            {importing ? '导入中...' : '📥 导入历史日志'}
          </button>
          <button
            onClick={() => {
              setShowCreateForm(true);
              setEditingLog(null);
              setFormData({
                version: '',
                type: 'minor',
                title: '',
                description: '',
                changes: ['']
              });
            }}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            ➕ 添加日志
          </button>
        </div>
      </div>

      {/* 创建/编辑表单 */}
      {showCreateForm && (
        <div className="bg-white border rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">
            {editingLog ? '编辑日志' : '创建新日志'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  版本号 *
                </label>
                <input
                  type="text"
                  value={formData.version}
                  onChange={(e) => setFormData(prev => ({ ...prev, version: e.target.value }))}
                  placeholder="如: v2.3.0"
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  类型 *
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="major">🚀 重大更新</option>
                  <option value="minor">✨ 功能更新</option>
                  <option value="patch">🔧 修复更新</option>
                  <option value="hotfix">🚨 紧急修复</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                标题
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="可选的更新标题"
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                描述
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="可选的详细描述"
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                变更列表 *
              </label>
              {formData.changes.map((change, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={change}
                    onChange={(e) => updateChange(index, e.target.value)}
                    placeholder="输入变更内容"
                    className="flex-1 border border-gray-300 rounded-md px-3 py-2"
                  />
                  {formData.changes.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeChange(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      ❌
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={addChangeField}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                ➕ 添加变更项
              </button>
            </div>

            <div className="flex space-x-3">
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                {editingLog ? '更新' : '创建'}
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowCreateForm(false);
                  setEditingLog(null);
                }}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      {/* 日志列表 */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold">现有日志</h3>
        </div>
        
        {loading ? (
          <div className="p-6 text-center">加载中...</div>
        ) : logs.length === 0 ? (
          <div className="p-6 text-center text-gray-500">暂无日志记录</div>
        ) : (
          <div className="divide-y divide-gray-200">
            {logs.map((log) => (
              <div key={log.id} className="p-4 hover:bg-gray-50">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="font-mono text-lg">{log.version}</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(log.type)}`}>
                        {log.type}
                      </span>
                      <span className="text-sm text-gray-500">
                        {new Date(log.date).toLocaleDateString()}
                      </span>
                    </div>
                    
                    {log.title && (
                      <h4 className="font-medium text-gray-900 mb-1">{log.title}</h4>
                    )}
                    
                    <div className="text-sm text-gray-600">
                      {log.changes.slice(0, 2).map((change, index) => (
                        <div key={index}>• {change}</div>
                      ))}
                      {log.changes.length > 2 && (
                        <div className="text-gray-400">... 还有 {log.changes.length - 2} 项</div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2 ml-4">
                    <button
                      onClick={() => handleEdit(log)}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      编辑
                    </button>
                    <button
                      onClick={() => handleDelete(log.id)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
