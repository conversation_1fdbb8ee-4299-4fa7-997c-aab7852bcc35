import { Request, Response } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse } from '../types';
import prisma from '../config/database';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

// 配置音频文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'audio');
    // 确保目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名：时间戳_随机字符串.扩展名
    const timestamp = Date.now();
    const ext = path.extname(file.originalname);
    const randomStr = Math.random().toString(36).substring(2, 8);
    cb(null, `${timestamp}_${randomStr}${ext}`);
  }
});

// 文件过滤器
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // 只允许音频文件
  if (file.mimetype.startsWith('audio/')) {
    cb(null, true);
  } else {
    cb(new Error('只允许上传音频文件'));
  }
};

// 配置multer
export const audioUpload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB限制
    fieldSize: 50 * 1024 * 1024, // 字段大小限制
  }
});

/**
 * 上传文章音频
 */
export const uploadArticleAudio = asyncHandler(async (req: Request, res: Response) => {
  const articleId = parseInt(req.params.articleId);
  const { title, description } = req.body;
  const file = req.file;
  const userId = (req as any).user?.userId;

  console.log('🎵 音频上传请求:', {
    articleId,
    title,
    fileSize: file?.size,
    fileName: file?.originalname,
    mimeType: file?.mimetype
  });

  if (!file) {
    return res.status(400).json({
      success: false,
      message: '请选择音频文件',
    });
  }

  if (!userId) {
    return res.status(401).json({
      success: false,
      message: '需要登录',
    });
  }

  // 检查文章是否存在
  const article = await prisma.article.findUnique({
    where: { id: articleId }
  });

  if (!article) {
    // 删除已上传的文件
    fs.unlinkSync(file.path);
    return res.status(404).json({
      success: false,
      message: '文章不存在',
    });
  }

  // 检查是否已有音频
  const existingAudio = await prisma.articleAudio.findUnique({
    where: { articleId }
  });

  if (existingAudio) {
    // 删除旧音频文件
    const oldFilePath = path.join(process.cwd(), 'uploads', 'audio', path.basename(existingAudio.audioUrl));
    if (fs.existsSync(oldFilePath)) {
      fs.unlinkSync(oldFilePath);
    }
    
    // 删除数据库记录
    await prisma.articleAudio.delete({
      where: { articleId }
    });
  }

  // 生成音频URL
  const audioUrl = `/uploads/audio/${file.filename}`;

  // 保存音频信息到数据库
  const audio = await prisma.articleAudio.create({
    data: {
      articleId,
      title: title || `${article.title} - 播客`,
      description,
      fileName: file.originalname,
      fileSize: file.size,
      mimeType: file.mimetype,
      audioUrl,
      uploadedBy: userId,
    },
    include: {
      uploader: {
        select: {
          id: true,
          username: true,
          name: true,
        }
      }
    }
  });

  const response: ApiResponse = {
    success: true,
    message: '音频上传成功',
    data: audio,
  };

  res.status(201).json(response);
});

/**
 * 获取文章音频信息
 */
export const getArticleAudio = asyncHandler(async (req: Request, res: Response) => {
  const articleId = parseInt(req.params.articleId);

  const audio = await prisma.articleAudio.findUnique({
    where: { articleId },
    include: {
      uploader: {
        select: {
          id: true,
          username: true,
          name: true,
        }
      }
    }
  });

  const response: ApiResponse = {
    success: true,
    message: audio ? '获取音频信息成功' : '该文章没有音频',
    data: audio,
  };

  res.status(200).json(response);
});

/**
 * 删除文章音频
 */
export const deleteArticleAudio = asyncHandler(async (req: Request, res: Response) => {
  const articleId = parseInt(req.params.articleId);
  const userId = (req as any).user?.userId;

  if (!userId) {
    return res.status(401).json({
      success: false,
      message: '需要登录',
    });
  }

  const audio = await prisma.articleAudio.findUnique({
    where: { articleId }
  });

  if (!audio) {
    return res.status(404).json({
      success: false,
      message: '音频不存在',
    });
  }

  // 删除文件
  const filePath = path.join(process.cwd(), 'uploads', 'audio', path.basename(audio.audioUrl));
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
  }

  // 删除数据库记录
  await prisma.articleAudio.delete({
    where: { articleId }
  });

  const response: ApiResponse = {
    success: true,
    message: '音频删除成功',
  };

  res.status(200).json(response);
});

/**
 * 更新音频信息
 */
export const updateArticleAudio = asyncHandler(async (req: Request, res: Response) => {
  const articleId = parseInt(req.params.articleId);
  const { title, description, duration } = req.body;
  const userId = (req as any).user?.userId;

  if (!userId) {
    return res.status(401).json({
      success: false,
      message: '需要登录',
    });
  }

  const audio = await prisma.articleAudio.findUnique({
    where: { articleId }
  });

  if (!audio) {
    return res.status(404).json({
      success: false,
      message: '音频不存在',
    });
  }

  const updatedAudio = await prisma.articleAudio.update({
    where: { articleId },
    data: {
      title,
      description,
      duration,
    },
    include: {
      uploader: {
        select: {
          id: true,
          username: true,
          name: true,
        }
      }
    }
  });

  const response: ApiResponse = {
    success: true,
    message: '音频信息更新成功',
    data: updatedAudio,
  };

  res.status(200).json(response);
});
