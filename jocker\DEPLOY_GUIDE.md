# 🚀 Jocker 一键部署指南

## 📋 部署脚本说明

### 前端部署
```bash
deploy-frontend.bat
```
- 构建前端项目
- 上传到服务器
- 自动处理错误

### 后端部署
```bash
deploy-backend.bat
```
- 构建后端项目
- 上传代码文件到服务器（不包含数据库）
- 更新数据库结构（保留现有数据）
- 安装依赖并重启服务

### 完整部署
```bash
deploy-all.bat
```
- 依次执行前端和后端部署
- 一键完成整个项目部署

## ⚠️ 注意事项

1. **编码问题**: 如果脚本显示乱码，请将bat文件编码改为GBK
2. **SSH配置**: 确保已配置SSH密钥，可以免密登录服务器
3. **权限问题**: 确保有服务器写入权限
4. **网络连接**: 确保网络连接稳定
5. **数据库安全**: 部署脚本会保留服务器上的数据库，只更新代码和结构

## 🔧 服务器信息

- **服务器**: 47.79.90.239
- **前端端口**: 3000
- **后端端口**: 5003
- **PM2服务名**: jocker-backend

## 📁 目录结构

```
jocker/
├── frontend/           # 前端项目
├── backend/           # 后端项目
├── logs/              # 日志文件
├── deploy-frontend.bat # 前端部署脚本
├── deploy-backend.bat  # 后端部署脚本
└── deploy-all.bat     # 完整部署脚本
```

## 🐛 故障排除

如果部署失败，请检查：
1. 网络连接是否正常
2. SSH密钥是否配置正确
3. 服务器磁盘空间是否充足
4. PM2服务是否正常运行
