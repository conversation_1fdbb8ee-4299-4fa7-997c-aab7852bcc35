import React from 'react';
import { Article } from '../types';

interface ArticleCardProps {
  article: Article;
  onNavigateToArticle: (id: number) => void;
}

export const ArticleCard: React.FC<ArticleCardProps> = ({ article, onNavigateToArticle }) => {

  return (
    <div className="flex flex-col rounded-lg shadow-lg overflow-hidden h-full transition-shadow duration-300 hover:shadow-2xl">
      <div className="flex-shrink-0">
        <button onClick={() => onNavigateToArticle(article.id)} className="block w-full">
            <img className="h-48 w-full object-cover" src={article.imageUrl} alt={article.title} />
        </button>
      </div>
      <div className="flex-1 bg-white p-6 flex flex-col justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-purple-700">
            {article.category}
          </p>
          <button onClick={() => onNavigateToArticle(article.id)} className="block mt-2 text-left">
            <p className="text-xl font-semibold text-gray-900 hover:text-purple-800">{article.title}</p>
            <p className="mt-3 text-base text-gray-500">{article.excerpt}</p>
          </button>
        </div>
        <div className="mt-6 flex items-center">
          <div className="text-sm text-gray-500">
            <p>{article.author}</p>
          </div>
        </div>
      </div>
    </div>
  );
};