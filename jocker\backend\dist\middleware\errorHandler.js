"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.notFoundHandler = exports.errorHandler = void 0;
const errorHandler = (error, req, res, next) => {
    console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        body: req.body,
        params: req.params,
        query: req.query,
    });
    let statusCode = 500;
    let message = '服务器内部错误';
    if (error.message.includes('不存在') || error.message.includes('未找到')) {
        statusCode = 404;
        message = error.message;
    }
    else if (error.message.includes('已存在') ||
        error.message.includes('已被') ||
        error.message.includes('验证失败') ||
        error.message.includes('无效')) {
        statusCode = 400;
        message = error.message;
    }
    else if (error.message.includes('无权限') ||
        error.message.includes('权限不足') ||
        error.message.includes('访问被拒绝')) {
        statusCode = 403;
        message = error.message;
    }
    else if (error.message.includes('认证') ||
        error.message.includes('Token') ||
        error.message.includes('登录')) {
        statusCode = 401;
        message = error.message;
    }
    const response = {
        success: false,
        message,
        error: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    };
    res.status(statusCode).json(response);
};
exports.errorHandler = errorHandler;
const notFoundHandler = (req, res) => {
    const response = {
        success: false,
        message: `路由 ${req.method} ${req.path} 不存在`,
    };
    res.status(404).json(response);
};
exports.notFoundHandler = notFoundHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
//# sourceMappingURL=errorHandler.js.map