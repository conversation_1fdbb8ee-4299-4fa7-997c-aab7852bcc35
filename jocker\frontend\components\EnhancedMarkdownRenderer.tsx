import React, { useEffect, useRef } from 'react';

interface EnhancedMarkdownRendererProps {
  content: string;
  className?: string;
}

/**
 * 增强的 Markdown 渲染器
 * 专门处理学术文章中的格式问题
 */
export const EnhancedMarkdownRenderer: React.FC<EnhancedMarkdownRendererProps> = ({ 
  content, 
  className = '' 
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current && window.renderMathInElement) {
      // 1. 预处理内容，修复常见问题
      const processedContent = preprocessContent(content);
      
      // 2. 设置 HTML 内容
      containerRef.current.innerHTML = processedContent;

      // 3. 渲染数学公式
      window.renderMathInElement(containerRef.current, {
        delimiters: [
          { left: '$$', right: '$$', display: true },
          { left: '$', right: '$', display: false },
          { left: '\\[', right: '\\]', display: true },
          { left: '\\(', right: '\\)', display: false }
        ],
        throwOnError: false,
        errorColor: '#cc0000',
        strict: false,
        trust: false,
        macros: {
          '\\RR': '\\mathbb{R}',
          '\\NN': '\\mathbb{N}',
          '\\ZZ': '\\mathbb{Z}',
          '\\QQ': '\\mathbb{Q}',
          '\\CC': '\\mathbb{C}',
        }
      });

      // 4. 后处理：修复渲染后的问题
      postProcessContent(containerRef.current);
    }
  }, [content]);

  return (
    <div 
      ref={containerRef} 
      className={`enhanced-markdown-content ${className}`}
      style={{ lineHeight: '1.6' }}
    />
  );
};

/**
 * 预处理内容，修复常见的 Markdown 问题
 */
function preprocessContent(content: string): string {
  let processed = content;

  // 1. 修复中文加粗标记
  processed = processed.replace(/\*\*图 (\d+)：\*\*/g, '**Figure $1:**');
  processed = processed.replace(/\*\*表 (\d+)：\*\*/g, '**Table $1:**');

  // 2. 修复加粗标记的空格问题
  processed = processed.replace(/([^\s\n*])\*\*([^*]+)\*\*([^\s\n*])/g, '$1 **$2** $3');

  // 3. 修复行首的加粗标记
  processed = processed.replace(/^\*\*([^*]+)\*\*(?=\s|$)/gm, '**$1**');

  // 4. 修复嵌套的加粗标记
  processed = processed.replace(/\*\*\*\*([^*]+)\*\*\*\*/g, '**$1**');

  // 5. 修复不完整的加粗标记
  processed = processed.replace(/\*\*([^*\n]+?)(?=\n|$)/g, '**$1**');

  // 6. 修复斜体标记问题
  processed = processed.replace(/([^\s\n*])\*([^*\s][^*]*[^*\s])\*([^\s\n*])/g, '$1 *$2* $3');

  // 7. 修复代码块标记
  processed = processed.replace(/```([^`\n]*)\n([\s\S]*?)```/g, (match, lang, code) => {
    return `\`\`\`${lang}\n${code.trim()}\n\`\`\``;
  });

  // 8. 修复行内代码
  processed = processed.replace(/([^\s\n`])`([^`]+)`([^\s\n`])/g, '$1 `$2` $3');

  // 9. 修复链接格式
  processed = processed.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '[$1]($2)');

  // 10. 修复标题格式
  processed = processed.replace(/^(#{1,6})\s*(.+)$/gm, '$1 $2');

  return processed;
}

/**
 * 后处理：修复渲染后的问题
 */
function postProcessContent(container: HTMLElement): void {
  // 1. 修复加粗文本的显示
  const boldElements = container.querySelectorAll('strong');
  boldElements.forEach(element => {
    if (element.textContent?.includes('**')) {
      element.textContent = element.textContent.replace(/\*\*/g, '');
    }
  });

  // 2. 修复图片占位符的样式
  const figurePlaceholders = container.querySelectorAll('.figure-placeholder');
  figurePlaceholders.forEach(element => {
    element.classList.add('text-center', 'py-4', 'bg-gray-50', 'border', 'border-gray-200', 'rounded-lg', 'text-gray-600', 'font-medium');
  });

  // 3. 修复表格样式
  const tables = container.querySelectorAll('table');
  tables.forEach(table => {
    table.classList.add('min-w-full', 'divide-y', 'divide-gray-200', 'my-6');
    
    const thead = table.querySelector('thead');
    if (thead) {
      thead.classList.add('bg-gray-50');
    }
    
    const ths = table.querySelectorAll('th');
    ths.forEach(th => {
      th.classList.add('px-6', 'py-3', 'text-left', 'text-xs', 'font-medium', 'text-gray-500', 'uppercase', 'tracking-wider');
    });
    
    const tds = table.querySelectorAll('td');
    tds.forEach(td => {
      td.classList.add('px-6', 'py-4', 'whitespace-nowrap', 'text-sm', 'text-gray-900');
    });
  });

  // 4. 修复代码块样式
  const codeBlocks = container.querySelectorAll('pre code');
  codeBlocks.forEach(code => {
    const pre = code.parentElement;
    if (pre) {
      pre.classList.add('bg-gray-100', 'rounded-lg', 'p-4', 'overflow-x-auto', 'my-4');
      code.classList.add('text-sm', 'font-mono');
    }
  });

  // 5. 修复行内代码样式
  const inlineCodes = container.querySelectorAll('code:not(pre code)');
  inlineCodes.forEach(code => {
    code.classList.add('bg-gray-100', 'px-2', 'py-1', 'rounded', 'text-sm', 'font-mono');
  });

  // 6. 修复引用块样式
  const blockquotes = container.querySelectorAll('blockquote');
  blockquotes.forEach(blockquote => {
    blockquote.classList.add('border-l-4', 'border-purple-500', 'pl-4', 'italic', 'text-gray-700', 'my-4');
  });

  // 7. 修复列表样式
  const uls = container.querySelectorAll('ul');
  uls.forEach(ul => {
    ul.classList.add('list-disc', 'list-inside', 'space-y-2', 'my-4');
  });

  const ols = container.querySelectorAll('ol');
  ols.forEach(ol => {
    ol.classList.add('list-decimal', 'list-inside', 'space-y-2', 'my-4');
  });

  // 8. 修复段落间距
  const paragraphs = container.querySelectorAll('p');
  paragraphs.forEach(p => {
    p.classList.add('mb-4');
  });
}

/**
 * 专门处理学术文章的 Markdown 渲染
 */
export const AcademicMarkdownRenderer: React.FC<EnhancedMarkdownRendererProps> = ({ 
  content, 
  className = '' 
}) => {
  return (
    <EnhancedMarkdownRenderer
      content={content}
      className={`academic-content prose prose-lg max-w-none prose-headings:font-serif prose-h2:text-2xl prose-h2:border-b prose-h2:border-gray-200 prose-h2:pb-2 prose-h2:mb-6 prose-h2:mt-8 prose-p:text-gray-700 prose-p:leading-relaxed prose-strong:text-gray-900 prose-strong:font-semibold ${className}`}
    />
  );
};

export default EnhancedMarkdownRenderer;
