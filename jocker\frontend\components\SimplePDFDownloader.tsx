import React, { useState } from 'react';
import { Article } from '../types';

interface SimplePDFDownloaderProps {
  article: Article;
  content: string;
}

// 生成DOI格式的文件名
const generateDOI = (article: Article): string => {
  const year = new Date(article.createdAt).getFullYear();
  const month = String(new Date(article.createdAt).getMonth() + 1).padStart(2, '0');
  const day = String(new Date(article.createdAt).getDate()).padStart(2, '0');

  // 清理标题，移除特殊字符，保留字母数字和空格
  const cleanTitle = article.title
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .toLowerCase()
    .substring(0, 50); // 限制长度

  return `10.1000_joker.${year}.${month}.${day}.${article.id}.${cleanTitle}`;
};

export const SimplePDFDownloader: React.FC<SimplePDFDownloaderProps> = ({ article, content }) => {
  const [isGenerating, setIsGenerating] = useState(false);

  const generatePDF = async () => {
    setIsGenerating(true);
    
    try {
      // 使用浏览器的打印功能生成 PDF
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error('无法打开打印窗口，请检查浏览器弹窗设置');
      }

      const pdfHTML = generatePrintHTML(article, content);
      
      printWindow.document.write(pdfHTML);
      printWindow.document.close();
      
      // 等待内容加载完成
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

    } catch (error) {
      console.error('PDF 生成失败:', error);
      alert('PDF 生成失败，请稍后重试。');
    } finally {
      setIsGenerating(false);
    }
  };

  // 生成打印友好的 HTML
  const generatePrintHTML = (article: Article, content: string): string => {
    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // 生成DOI格式的文件名
    const doiFileName = generateDOI(article);

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${doiFileName}</title>
        <style>
          @page {
            size: A4;
            margin: 2cm;
          }
          
          @media print {
            body { 
              font-family: 'Times New Roman', serif;
              font-size: 12pt;
              line-height: 1.6;
              color: #000;
              background: white;
            }
            
            .no-print { display: none !important; }
            
            h1, h2, h3 { 
              page-break-after: avoid;
              color: #000;
            }
            
            .page-break {
              page-break-before: always;
            }

            /* 确保上标样式在PDF中正确显示 */
            sup {
              vertical-align: super !important;
              font-size: 0.75em !important;
              line-height: 0 !important;
            }

            /* 确保参考文献序号在PDF中显示 */
            .space-y-4 > div {
              display: flex !important;
              margin-bottom: 1rem !important;
            }

            .flex-shrink-0 {
              flex-shrink: 0 !important;
              width: 2rem !important;
            }

            .flex-1 {
              flex: 1 !important;
            }

            /* 防止引用链接在PDF中断行 */
            sup a {
              white-space: nowrap !important;
              text-decoration: none !important;
              color: #2563eb !important;
            }
          }
          
          body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.6;
            color: #000;
            max-width: 100%;
            margin: 0;
            padding: 20px;
          }
          
          .journal-header {
            text-align: center;
            border-bottom: 2px solid #7C3AED;
            padding-bottom: 20px;
            margin-bottom: 30px;
          }
          
          .journal-title {
            font-size: 24pt;
            font-weight: bold;
            color: #7C3AED;
            margin: 0;
          }
          
          .journal-info {
            font-size: 12pt;
            color: #666;
            margin: 5px 0;
          }
          
          .article-category {
            font-size: 10pt;
            color: #7C3AED;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin: 0;
          }
          
          .article-title {
            font-size: 18pt;
            font-weight: bold;
            color: #1F2937;
            margin: 10px 0 20px 0;
            line-height: 1.3;
          }
          
          .article-meta {
            font-size: 12pt;
            color: #4B5563;
            margin: 0;
          }
          
          .abstract-box {
            background-color: #F9FAFB;
            border-left: 4px solid #7C3AED;
            padding: 20px;
            margin: 30px 0;
          }
          
          .abstract-title {
            font-size: 14pt;
            font-weight: bold;
            color: #1F2937;
            margin: 0 0 10px 0;
          }
          
          .abstract-content {
            font-size: 11pt;
            color: #374151;
            line-height: 1.6;
            margin: 0;
          }
          
          .keywords {
            font-size: 10pt;
            color: #6B7280;
            margin: 15px 0 0 0;
          }
          
          .content {
            font-size: 11pt;
            line-height: 1.7;
            color: #374151;
          }
          
          .content h2 {
            font-size: 14pt;
            font-weight: bold;
            color: #1F2937;
            margin: 25px 0 15px 0;
            border-bottom: 1px solid #E5E7EB;
            padding-bottom: 5px;
          }
          
          .content h3 {
            font-size: 12pt;
            font-weight: bold;
            color: #374151;
            margin: 20px 0 10px 0;
          }
          
          .content p {
            margin: 12px 0;
          }
          
          .figure-placeholder {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background-color: #F3F4F6;
            border: 1px dashed #9CA3AF;
            font-style: italic;
          }
          
          .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #E5E7EB;
            text-align: center;
            font-size: 9pt;
            color: #6B7280;
          }
        </style>
      </head>
      <body>
        <!-- Journal Header -->
        <div class="journal-header">
          <h1 class="journal-title">Joker: Journal of Satirical Science</h1>
          <p class="journal-info">
            Volume ${new Date().getFullYear()}, Issue ${Math.ceil(new Date().getMonth() / 3)}, ${new Date().getFullYear()}
          </p>
          <p class="journal-info">
            DOI: 10.1000/joker.${new Date().getFullYear()}.${article.id}
          </p>
        </div>

        <!-- Article Metadata -->
        <div>
          <p class="article-category">${article.category}</p>
          <h2 class="article-title">${article.title}</h2>
          <p class="article-meta"><strong>Author:</strong> ${article.author}</p>
          <p class="article-meta"><strong>Published:</strong> ${currentDate}</p>
        </div>

        <!-- Abstract -->
        <div class="abstract-box">
          <h3 class="abstract-title">Abstract</h3>
          <p class="abstract-content">${article.excerpt}</p>
          <p class="keywords">
            <strong>Keywords:</strong> satirical science, methodology, ${article.category.toLowerCase()}, peer review, academic humor
          </p>
        </div>

        <!-- Article Content -->
        <div class="content">
          ${formatContentForPrint(content)}
        </div>

        <!-- Footer -->
        <div class="footer">
          <p>© ${new Date().getFullYear()} Joker: Journal of Satirical Science. All rights reserved.</p>
          <p>Generated on ${currentDate}</p>
        </div>
      </body>
      </html>
    `;
  };

  // 格式化内容用于打印
  const formatContentForPrint = (content: string): string => {
    if (!content) return '';

    // 移除 Figures 部分
    let formatted = content.replace(/## Figures\s*[\s\S]*$/, '');

    // 转换 Markdown 标题为 HTML
    formatted = formatted.replace(/^## (.+)$/gm, '<h2>$1</h2>');
    formatted = formatted.replace(/^### (.+)$/gm, '<h3>$1</h3>');

    // 转换加粗文本
    formatted = formatted.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');

    // 转换斜体文本
    formatted = formatted.replace(/\*(.+?)\*/g, '<em>$1</em>');

    // 转换段落
    formatted = formatted.replace(/\n\n/g, '</p><p>');
    formatted = '<p>' + formatted + '</p>';

    // 处理图片占位符
    formatted = formatted.replace(/\[Figure \d+: ([^\]]+)\]/g, '<div class="figure-placeholder">Figure: $1</div>');

    return formatted;
  };

  return (
    <button
      onClick={generatePDF}
      disabled={isGenerating || !content}
      className="w-full px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
    >
      {isGenerating ? (
        <>
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          准备打印...
        </>
      ) : (
        <>
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          📄 Download PDF
        </>
      )}
    </button>
  );
};
