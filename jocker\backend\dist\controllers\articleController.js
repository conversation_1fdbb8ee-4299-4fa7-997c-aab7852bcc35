"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getArticleFigures = exports.likeExistingArticle = exports.getFeatured = exports.getTrending = exports.updateArticleViews = exports.batchDeleteArticles = exports.getArticleRealViews = exports.deleteExistingArticle = exports.updateExistingArticle = exports.createNewArticle = exports.getArticle = exports.getArticleList = void 0;
const articleService_1 = require("../services/articleService");
const errorHandler_1 = require("../middleware/errorHandler");
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
exports.getArticleList = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const query = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10,
        sortBy: req.query.sortBy || 'createdAt',
        sortOrder: req.query.sortOrder || 'desc',
        q: req.query.q,
        category: req.query.category,
        author: req.query.author,
        published: req.query.published ? req.query.published === 'true' : undefined,
        featured: req.query.featured ? req.query.featured === 'true' : undefined,
    };
    const result = await (0, articleService_1.getArticles)(query);
    const response = {
        success: true,
        message: '获取文章列表成功',
        data: result,
    };
    res.status(200).json(response);
});
exports.getArticle = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章 ID',
        });
    }
    const article = await (0, articleService_1.getArticleById)(id);
    const skipView = req.query.skip_view === 'true';
    if (article && !skipView) {
        const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
        const userAgent = req.get('User-Agent') || '';
        const fingerprint = req.body.fingerprint || req.query.fingerprint;
        const userId = req.user?.id || null;
        try {
            const existingView = await prisma.articleView.findFirst({
                where: {
                    articleId: id,
                    OR: [
                        userId ? { userId: userId } : {},
                        {
                            ipAddress: ipAddress,
                            fingerprint: fingerprint || undefined
                        }
                    ]
                }
            });
            if (!existingView) {
                await prisma.$transaction(async (tx) => {
                    await tx.articleView.create({
                        data: {
                            articleId: id,
                            userId: userId,
                            ipAddress: ipAddress,
                            userAgent: userAgent,
                            fingerprint: fingerprint
                        }
                    });
                    await tx.article.update({
                        where: { id: id },
                        data: { views: { increment: 1 } }
                    });
                });
                article.views = (article.views || 0) + 1;
                console.log(`📊 新观看记录：文章${id}，IP: ${ipAddress}，用户: ${userId || '匿名'}`);
            }
            else {
                console.log(`📊 重复访问：文章${id}，IP: ${ipAddress}，用户: ${userId || '匿名'}`);
            }
        }
        catch (error) {
            console.error('记录观看次数失败:', error);
        }
    }
    const response = {
        success: true,
        message: '获取文章成功',
        data: article,
    };
    res.status(200).json(response);
});
exports.createNewArticle = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const articleData = req.body;
    const article = await (0, articleService_1.createArticle)(articleData);
    const response = {
        success: true,
        message: '创建文章成功',
        data: article,
    };
    res.status(201).json(response);
});
exports.updateExistingArticle = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const id = parseInt(req.params.id);
    const articleData = req.body;
    if (isNaN(id)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章 ID',
        });
    }
    const article = await (0, articleService_1.updateArticle)(id, articleData);
    const response = {
        success: true,
        message: '更新文章成功',
        data: article,
    };
    res.status(200).json(response);
});
exports.deleteExistingArticle = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章 ID',
        });
    }
    await (0, articleService_1.deleteArticle)(id);
    const response = {
        success: true,
        message: '删除文章成功',
    };
    res.status(200).json(response);
});
exports.getArticleRealViews = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章 ID',
        });
    }
    const viewCount = await prisma.articleView.count({
        where: { articleId: id }
    });
    const response = {
        success: true,
        message: '获取观看次数成功',
        data: { views: viewCount }
    };
    res.status(200).json(response);
});
exports.batchDeleteArticles = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { articleIds } = req.body;
    if (!articleIds || !Array.isArray(articleIds) || articleIds.length === 0) {
        return res.status(400).json({
            success: false,
            message: '请提供有效的文章ID列表',
        });
    }
    const validIds = articleIds.filter(id => !isNaN(parseInt(id))).map(id => parseInt(id));
    if (validIds.length === 0) {
        return res.status(400).json({
            success: false,
            message: '没有有效的文章ID',
        });
    }
    await prisma.$transaction(async (tx) => {
        await tx.figure.deleteMany({
            where: { articleId: { in: validIds } }
        });
        await tx.article.deleteMany({
            where: { id: { in: validIds } }
        });
    });
    const response = {
        success: true,
        message: `成功删除 ${validIds.length} 篇文章`,
        data: { deletedCount: validIds.length }
    };
    res.status(200).json(response);
});
exports.updateArticleViews = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const id = parseInt(req.params.id);
    const { views } = req.body;
    if (isNaN(id)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章 ID',
        });
    }
    if (typeof views !== 'number' || views < 0) {
        return res.status(400).json({
            success: false,
            message: '观看次数必须是非负整数',
        });
    }
    const updatedArticle = await prisma.article.update({
        where: { id },
        data: { views },
        select: { id: true, title: true, views: true }
    });
    const response = {
        success: true,
        message: '更新观看次数成功',
        data: updatedArticle
    };
    res.status(200).json(response);
});
exports.getTrending = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const limit = parseInt(req.query.limit) || 5;
    const articles = await (0, articleService_1.getTrendingArticles)(limit);
    const response = {
        success: true,
        message: '获取热门文章成功',
        data: articles,
    };
    res.status(200).json(response);
});
exports.getFeatured = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const limit = parseInt(req.query.limit) || 3;
    const articles = await (0, articleService_1.getFeaturedArticles)(limit);
    const response = {
        success: true,
        message: '获取推荐文章成功',
        data: articles,
    };
    res.status(200).json(response);
});
exports.likeExistingArticle = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章 ID',
        });
    }
    const article = await (0, articleService_1.likeArticle)(id);
    const response = {
        success: true,
        message: '点赞成功',
        data: article,
    };
    res.status(200).json(response);
});
exports.getArticleFigures = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const articleId = parseInt(id);
    if (isNaN(articleId)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章ID',
        });
    }
    try {
        const article = await prisma.article.findUnique({
            where: { id: articleId },
        });
        if (!article) {
            return res.status(404).json({
                success: false,
                message: '文章不存在',
            });
        }
        const figures = await prisma.figure.findMany({
            where: {
                articleId: articleId,
            },
            orderBy: { figureNumber: 'asc' },
            select: {
                id: true,
                figureNumber: true,
                title: true,
                description: true,
                caption: true,
                imageUrl: true,
                thumbnailUrl: true,
                width: true,
                height: true,
                status: true,
                createdAt: true,
                updatedAt: true,
            },
        });
        const response = {
            success: true,
            message: '获取图片列表成功',
            data: {
                figures: figures,
                count: figures.length
            }
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('获取文章图片失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '获取图片失败',
        });
    }
});
//# sourceMappingURL=articleController.js.map