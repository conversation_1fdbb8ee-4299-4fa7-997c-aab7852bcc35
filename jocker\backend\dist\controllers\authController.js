"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyToken = exports.getProfile = exports.login = exports.register = void 0;
const authService_1 = require("../services/authService");
const errorHandler_1 = require("../middleware/errorHandler");
exports.register = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userData = req.body;
    const result = await (0, authService_1.registerUser)(userData);
    const response = {
        success: true,
        message: '注册成功',
        data: result,
    };
    res.status(201).json(response);
});
exports.login = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const loginData = req.body;
    const result = await (0, authService_1.loginUser)(loginData);
    const response = {
        success: true,
        message: '登录成功',
        data: result,
    };
    res.status(200).json(response);
});
exports.getProfile = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            message: '未认证',
        });
    }
    const user = await (0, authService_1.getUserById)(req.user.userId);
    const response = {
        success: true,
        message: '获取用户信息成功',
        data: user,
    };
    return res.status(200).json(response);
});
exports.verifyToken = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            message: 'Token 无效',
        });
    }
    const response = {
        success: true,
        message: 'Token 有效',
        data: {
            valid: true,
            user: req.user,
        },
    };
    return res.status(200).json(response);
});
//# sourceMappingURL=authController.js.map