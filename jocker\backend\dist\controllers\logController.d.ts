import { Request, Response } from 'express';
export declare const getSystemLogs: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const createSystemLog: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const updateSystemLog: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const deleteSystemLog: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getLogStats: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const importHistoryLogs: (req: Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=logController.d.ts.map