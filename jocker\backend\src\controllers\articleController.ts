import { Request, Response } from 'express';
import {
  getArticles,
  getArticleById,
  createArticle,
  updateArticle,
  deleteArticle,
  getTrendingArticles,
  getFeaturedArticles,
  likeArticle,
} from '../services/articleService';
import { ApiResponse, SearchQuery, CreateArticleRequest, UpdateArticleRequest } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 获取文章列表
 */
export const getArticleList = asyncHandler(async (req: Request, res: Response) => {
  const query: SearchQuery = {
    page: parseInt(req.query.page as string) || 1,
    limit: parseInt(req.query.limit as string) || 10,
    sortBy: req.query.sortBy as string || 'createdAt',
    sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
    q: req.query.q as string,
    category: req.query.category as string,
    author: req.query.author as string,
    published: req.query.published ? req.query.published === 'true' : undefined,
    featured: req.query.featured ? req.query.featured === 'true' : undefined,
  };

  const result = await getArticles(query);

  const response: ApiResponse = {
    success: true,
    message: '获取文章列表成功',
    data: result,
  };

  res.status(200).json(response);
});

/**
 * 获取单篇文章
 */
export const getArticle = asyncHandler(async (req: Request, res: Response) => {
  const id = parseInt(req.params.id);
  
  if (isNaN(id)) {
    return res.status(400).json({
      success: false,
      message: '无效的文章 ID',
    });
  }

  const article = await getArticleById(id);

  // 检查是否跳过观看统计
  const skipView = req.query.skip_view === 'true';

  // 记录观看次数（去重）
  if (article && !skipView) {
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || '';
    const fingerprint = req.body.fingerprint || req.query.fingerprint as string;

    // 获取当前用户ID（如果已登录）
    const userId = (req as any).user?.id || null;

    try {
      // 检查是否已经观看过
      const existingView = await prisma.articleView.findFirst({
        where: {
          articleId: id,
          OR: [
            // 如果用户已登录，检查用户ID
            userId ? { userId: userId } : {},
            // 否则检查IP和指纹
            {
              ipAddress: ipAddress,
              fingerprint: fingerprint || undefined
            }
          ]
        }
      });

      // 如果没有观看记录，创建新记录并增加观看次数
      if (!existingView) {
        await prisma.$transaction(async (tx) => {
          // 创建观看记录
          await tx.articleView.create({
            data: {
              articleId: id,
              userId: userId,
              ipAddress: ipAddress,
              userAgent: userAgent,
              fingerprint: fingerprint
            }
          });

          // 增加文章观看次数
          await tx.article.update({
            where: { id: id },
            data: { views: { increment: 1 } }
          });
        });

        // 更新返回的文章数据中的观看次数
        article.views = (article.views || 0) + 1;
        console.log(`📊 新观看记录：文章${id}，IP: ${ipAddress}，用户: ${userId || '匿名'}`);
      } else {
        console.log(`📊 重复访问：文章${id}，IP: ${ipAddress}，用户: ${userId || '匿名'}`);
      }
    } catch (error) {
      console.error('记录观看次数失败:', error);
      // 即使记录失败，也不影响文章内容的返回
    }
  }

  const response: ApiResponse = {
    success: true,
    message: '获取文章成功',
    data: article,
  };

  res.status(200).json(response);
});

/**
 * 创建文章
 */
export const createNewArticle = asyncHandler(async (req: Request, res: Response) => {
  const articleData: CreateArticleRequest = req.body;

  const article = await createArticle(articleData);

  const response: ApiResponse = {
    success: true,
    message: '创建文章成功',
    data: article,
  };

  res.status(201).json(response);
});

/**
 * 更新文章
 */
export const updateExistingArticle = asyncHandler(async (req: Request, res: Response) => {
  const id = parseInt(req.params.id);
  const articleData: UpdateArticleRequest = req.body;

  if (isNaN(id)) {
    return res.status(400).json({
      success: false,
      message: '无效的文章 ID',
    });
  }

  const article = await updateArticle(id, articleData);

  const response: ApiResponse = {
    success: true,
    message: '更新文章成功',
    data: article,
  };

  res.status(200).json(response);
});

/**
 * 删除文章
 */
export const deleteExistingArticle = asyncHandler(async (req: Request, res: Response) => {
  const id = parseInt(req.params.id);

  if (isNaN(id)) {
    return res.status(400).json({
      success: false,
      message: '无效的文章 ID',
    });
  }

  await deleteArticle(id);

  const response: ApiResponse = {
    success: true,
    message: '删除文章成功',
  };

  res.status(200).json(response);
});

/**
 * 获取文章真实观看次数（基于观看记录）
 */
export const getArticleRealViews = asyncHandler(async (req: Request, res: Response) => {
  const id = parseInt(req.params.id);

  if (isNaN(id)) {
    return res.status(400).json({
      success: false,
      message: '无效的文章 ID',
    });
  }

  const viewCount = await prisma.articleView.count({
    where: { articleId: id }
  });

  const response: ApiResponse = {
    success: true,
    message: '获取观看次数成功',
    data: { views: viewCount }
  };

  res.status(200).json(response);
});

/**
 * 批量删除文章
 */
export const batchDeleteArticles = asyncHandler(async (req: Request, res: Response) => {
  const { articleIds } = req.body;

  if (!articleIds || !Array.isArray(articleIds) || articleIds.length === 0) {
    return res.status(400).json({
      success: false,
      message: '请提供有效的文章ID列表',
    });
  }

  // 验证所有ID都是有效的数字
  const validIds = articleIds.filter(id => !isNaN(parseInt(id))).map(id => parseInt(id));

  if (validIds.length === 0) {
    return res.status(400).json({
      success: false,
      message: '没有有效的文章ID',
    });
  }

  // 批量删除文章及相关数据
  await prisma.$transaction(async (tx) => {
    // 删除相关的图片
    await tx.figure.deleteMany({
      where: { articleId: { in: validIds } }
    });

    // 删除文章
    await tx.article.deleteMany({
      where: { id: { in: validIds } }
    });
  });

  const response: ApiResponse = {
    success: true,
    message: `成功删除 ${validIds.length} 篇文章`,
    data: { deletedCount: validIds.length }
  };

  res.status(200).json(response);
});

/**
 * 更新文章观看次数（管理员功能）
 */
export const updateArticleViews = asyncHandler(async (req: Request, res: Response) => {
  const id = parseInt(req.params.id);
  const { views } = req.body;

  if (isNaN(id)) {
    return res.status(400).json({
      success: false,
      message: '无效的文章 ID',
    });
  }

  if (typeof views !== 'number' || views < 0) {
    return res.status(400).json({
      success: false,
      message: '观看次数必须是非负整数',
    });
  }

  const updatedArticle = await prisma.article.update({
    where: { id },
    data: { views },
    select: { id: true, title: true, views: true }
  });

  const response: ApiResponse = {
    success: true,
    message: '更新观看次数成功',
    data: updatedArticle
  };

  res.status(200).json(response);
});

/**
 * 获取热门文章
 */
export const getTrending = asyncHandler(async (req: Request, res: Response) => {
  const limit = parseInt(req.query.limit as string) || 5;
  
  const articles = await getTrendingArticles(limit);

  const response: ApiResponse = {
    success: true,
    message: '获取热门文章成功',
    data: articles,
  };

  res.status(200).json(response);
});

/**
 * 获取推荐文章
 */
export const getFeatured = asyncHandler(async (req: Request, res: Response) => {
  const limit = parseInt(req.query.limit as string) || 3;
  
  const articles = await getFeaturedArticles(limit);

  const response: ApiResponse = {
    success: true,
    message: '获取推荐文章成功',
    data: articles,
  };

  res.status(200).json(response);
});

/**
 * 点赞文章
 */
export const likeExistingArticle = asyncHandler(async (req: Request, res: Response) => {
  const id = parseInt(req.params.id);

  if (isNaN(id)) {
    return res.status(400).json({
      success: false,
      message: '无效的文章 ID',
    });
  }

  const article = await likeArticle(id);

  const response: ApiResponse = {
    success: true,
    message: '点赞成功',
    data: article,
  };

  res.status(200).json(response);
});

/**
 * 获取文章图片列表（公共接口）
 * @desc 获取指定文章的所有图片，不需要认证
 * @route GET /api/articles/:id/figures
 * @access Public
 */
export const getArticleFigures = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const articleId = parseInt(id);

  if (isNaN(articleId)) {
    return res.status(400).json({
      success: false,
      message: '无效的文章ID',
    });
  }

  try {
    // 检查文章是否存在
    const article = await prisma.article.findUnique({
      where: { id: articleId },
    });

    if (!article) {
      return res.status(404).json({
        success: false,
        message: '文章不存在',
      });
    }

    // 获取文章的所有图片（包括失败的图片，以便重新生成）
    const figures = await prisma.figure.findMany({
      where: {
        articleId: articleId,
        // 返回所有状态的图片，包括 completed, failed, pending
      },
      orderBy: { figureNumber: 'asc' },
      select: {
        id: true,
        figureNumber: true,
        title: true,
        description: true,
        caption: true,
        imageUrl: true,
        thumbnailUrl: true,
        width: true,
        height: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    const response: ApiResponse = {
      success: true,
      message: '获取图片列表成功',
      data: {
        figures: figures,
        count: figures.length
      }
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('获取文章图片失败:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '获取图片失败',
    });
  }
});
