import { Request, Response } from 'express';
import { registerUser, loginUser, getUserById } from '../services/authService';
import { ApiResponse, LoginRequest, RegisterRequest } from '../types';
import { asyncHandler } from '../middleware/errorHandler';

/**
 * 用户注册
 */
export const register = asyncHandler(async (req: Request, res: Response) => {
  const userData: RegisterRequest = req.body;
  
  const result = await registerUser(userData);
  
  const response: ApiResponse = {
    success: true,
    message: '注册成功',
    data: result,
  };
  
  res.status(201).json(response);
});

/**
 * 用户登录
 */
export const login = asyncHandler(async (req: Request, res: Response) => {
  const loginData: LoginRequest = req.body;
  
  const result = await loginUser(loginData);
  
  const response: ApiResponse = {
    success: true,
    message: '登录成功',
    data: result,
  };
  
  res.status(200).json(response);
});

/**
 * 获取当前用户信息
 */
export const getProfile = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: '未认证',
    });
  }

  const user = await getUserById(req.user.userId);

  const response: ApiResponse = {
    success: true,
    message: '获取用户信息成功',
    data: user,
  };

  return res.status(200).json(response);
});

/**
 * 验证 Token
 */
export const verifyToken = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Token 无效',
    });
  }

  const response: ApiResponse = {
    success: true,
    message: 'Token 有效',
    data: {
      valid: true,
      user: req.user,
    },
  };

  return res.status(200).json(response);
});
