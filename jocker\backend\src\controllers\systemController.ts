import { Request, Response } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { ApiResponse } from '../types';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';
import prisma from '../config/database';

const execAsync = promisify(exec);

/**
 * 获取系统资源使用情况
 */
export const getSystemStats = asyncHandler(async (req: Request, res: Response) => {
  try {
    // 获取磁盘使用情况
    const { stdout: diskUsage } = await execAsync('df -h /var/www/jocker');
    const diskLines = diskUsage.split('\n');
    const diskInfo = diskLines[1].split(/\s+/);
    
    // 获取应用目录大小
    const { stdout: appSize } = await execAsync('du -sh /var/www/jocker/jocker');
    const totalAppSize = appSize.split('\t')[0];
    
    // 获取各个组件大小
    const { stdout: componentSizes } = await execAsync('du -sh /var/www/jocker/jocker/*');
    const components = componentSizes.split('\n').filter(line => line.trim()).map(line => {
      const [size, path] = line.split('\t');
      const name = path.split('/').pop();
      return { name, size };
    });
    
    // 获取音频文件统计
    let audioStats = { totalSize: '0B', fileCount: 0 };
    try {
      const { stdout: audioSize } = await execAsync('du -sh /var/www/jocker/jocker/backend/uploads/audio 2>/dev/null || echo "0B"');
      const { stdout: audioCount } = await execAsync('find /var/www/jocker/jocker/backend/uploads/audio -type f 2>/dev/null | wc -l || echo "0"');
      audioStats = {
        totalSize: audioSize.split('\t')[0] || '0B',
        fileCount: parseInt(audioCount.trim()) || 0
      };
    } catch (error) {
      console.log('音频目录不存在或为空');
    }
    
    // 获取数据库统计
    const [articleCount, userCount, audioCount] = await Promise.all([
      prisma.article.count(),
      prisma.user.count(),
      prisma.articleAudio.count()
    ]);
    
    // 获取数据库文件大小
    let dbSize = '0B';
    try {
      const dbPath = path.join(process.cwd(), 'prisma/dev.db');
      const stats = await fs.stat(dbPath);
      dbSize = formatBytes(stats.size);
    } catch (error) {
      console.log('无法获取数据库文件大小');
    }
    
    // 获取内存使用情况
    const { stdout: memInfo } = await execAsync('free -h');
    const memLines = memInfo.split('\n');
    const memData = memLines[1].split(/\s+/);
    
    // 获取CPU使用情况
    const { stdout: cpuInfo } = await execAsync('top -bn1 | grep "Cpu(s)" | awk \'{print $2}\' | cut -d\'%\' -f1');
    
    const systemStats = {
      disk: {
        total: diskInfo[1],
        used: diskInfo[2],
        available: diskInfo[3],
        usagePercent: diskInfo[4]
      },
      application: {
        totalSize: totalAppSize,
        components: components
      },
      audio: audioStats,
      database: {
        size: dbSize,
        articles: articleCount,
        users: userCount,
        audioFiles: audioCount
      },
      memory: {
        total: memData[1],
        used: memData[2],
        free: memData[3]
      },
      cpu: {
        usage: cpuInfo.trim() + '%'
      },
      uptime: await getUptime()
    };

    const response: ApiResponse = {
      success: true,
      message: '系统统计信息获取成功',
      data: systemStats,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('获取系统统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取系统统计失败',
    });
  }
});

/**
 * 获取详细的存储分析
 */
export const getStorageAnalysis = asyncHandler(async (req: Request, res: Response) => {
  try {
    // 获取各类文件的详细统计
    const analysis = {
      audioFiles: await getAudioFileAnalysis(),
      images: await getImageAnalysis(),
      database: await getDatabaseAnalysis(),
      logs: await getLogAnalysis()
    };

    const response: ApiResponse = {
      success: true,
      message: '存储分析获取成功',
      data: analysis,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('获取存储分析失败:', error);
    res.status(500).json({
      success: false,
      message: '获取存储分析失败',
    });
  }
});

/**
 * 清理临时文件和缓存
 */
export const cleanupStorage = asyncHandler(async (req: Request, res: Response) => {
  try {
    const cleanupResults = [];

    // 清理日志文件（保留最近7天）
    try {
      await execAsync('find /var/log -name "*.log" -mtime +7 -delete 2>/dev/null || true');
      cleanupResults.push('清理了7天前的日志文件');
    } catch (error) {
      console.log('日志清理失败:', error);
    }

    // 清理npm缓存
    try {
      await execAsync('npm cache clean --force 2>/dev/null || true');
      cleanupResults.push('清理了npm缓存');
    } catch (error) {
      console.log('npm缓存清理失败:', error);
    }

    const response: ApiResponse = {
      success: true,
      message: '存储清理完成',
      data: { cleanupResults },
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('存储清理失败:', error);
    res.status(500).json({
      success: false,
      message: '存储清理失败',
    });
  }
});

// 辅助函数
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i];
}

async function getUptime(): Promise<string> {
  try {
    const { stdout } = await execAsync('uptime -p');
    return stdout.trim();
  } catch (error) {
    return '未知';
  }
}

async function getAudioFileAnalysis() {
  try {
    const audioFiles = await prisma.articleAudio.findMany({
      select: {
        fileName: true,
        fileSize: true,
        mimeType: true,
        createdAt: true,
        article: {
          select: {
            title: true
          }
        }
      },
      orderBy: {
        fileSize: 'desc'
      }
    });

    const totalSize = audioFiles.reduce((sum, file) => sum + file.fileSize, 0);
    const avgSize = audioFiles.length > 0 ? totalSize / audioFiles.length : 0;

    return {
      totalFiles: audioFiles.length,
      totalSize: formatBytes(totalSize),
      averageSize: formatBytes(avgSize),
      largestFiles: audioFiles.slice(0, 5).map(file => ({
        fileName: file.fileName,
        size: formatBytes(file.fileSize),
        articleTitle: file.article.title,
        uploadDate: file.createdAt
      }))
    };
  } catch (error) {
    return { totalFiles: 0, totalSize: '0B', averageSize: '0B', largestFiles: [] };
  }
}

async function getImageAnalysis() {
  try {
    const { stdout } = await execAsync('find /var/www/jocker/jocker -name "*.jpg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp" | wc -l');
    const imageCount = parseInt(stdout.trim()) || 0;
    
    const { stdout: imageSize } = await execAsync('find /var/www/jocker/jocker -name "*.jpg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp" -exec du -ch {} + | tail -1 | cut -f1');
    
    return {
      totalFiles: imageCount,
      totalSize: imageSize.trim() || '0B'
    };
  } catch (error) {
    return { totalFiles: 0, totalSize: '0B' };
  }
}

async function getDatabaseAnalysis() {
  try {
    const dbPath = path.join(process.cwd(), 'prisma/dev.db');
    const stats = await fs.stat(dbPath);
    
    const tableStats = await Promise.all([
      prisma.article.count(),
      prisma.user.count(),
      prisma.articleAudio.count(),
      prisma.figure.count()
    ]);

    return {
      fileSize: formatBytes(stats.size),
      tables: {
        articles: tableStats[0],
        users: tableStats[1],
        audioFiles: tableStats[2],
        figures: tableStats[3]
      }
    };
  } catch (error) {
    return { fileSize: '0B', tables: {} };
  }
}

async function getLogAnalysis() {
  try {
    const { stdout } = await execAsync('du -sh /var/log 2>/dev/null || echo "0B"');
    return {
      totalSize: stdout.split('\t')[0] || '0B'
    };
  } catch (error) {
    return { totalSize: '0B' };
  }
}
