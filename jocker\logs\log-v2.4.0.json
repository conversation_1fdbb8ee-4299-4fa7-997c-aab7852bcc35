{"version": "v2.4.0", "type": "minor", "title": "文章列表封面显示与一键部署优化", "description": "优化文章列表界面显示效果，添加文章封面图片，完善一键部署系统，修复日志功能数据库问题", "changes": ["🖼️ 文章列表封面显示：在文章列表页面右侧添加24x24像素的文章封面图片展示", "🎨 智能图片处理：支持真实封面图片显示，加载失败时自动回退到渐变背景默认图标", "📱 响应式布局优化：使用flexbox重新设计文章列表布局，左侧信息右侧封面", "🚀 一键部署系统：创建完整的bat脚本部署系统，支持前端、后端和完整项目部署", "🛡️ 数据库安全保护：部署脚本只上传代码文件，完全避免覆盖服务器数据库", "🔧 数据库结构更新：修复system_logs表缺失问题，恢复日志功能正常工作", "📊 滚动组件优化：移除多余的标题信息，增加组件高度提升视觉效果", "🧹 服务器清理工具：创建服务器空间清理脚本，可清理音频文件和备份文件", "📝 部署文档完善：更新部署指南，明确数据库保护机制和注意事项", "🔄 自动数据库迁移：部署时自动运行prisma db push更新数据库结构"]}