import prisma from '../src/config/database';
import { hashPassword } from '../src/utils/password';

/**
 * 创建默认管理员账号
 */
async function createDefaultAdmin() {
  try {
    console.log('🔍 检查是否已存在管理员账号...');

    // 检查是否已存在管理员
    const existingAdmin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (existingAdmin) {
      console.log('✅ 管理员账号已存在:');
      console.log(`📧 邮箱: ${existingAdmin.email}`);
      console.log(`👤 用户名: ${existingAdmin.username}`);
      console.log(`🔑 密码: jocker2024 (默认密码)`);
      return;
    }

    console.log('🚀 创建默认管理员账号...');

    // 创建管理员账号
    const hashedPassword = await hashPassword('jocker2024');
    
    const admin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'admin',
        password: hashedPassword,
        name: 'Jocker Admin',
        role: 'ADMIN',
      },
    });

    console.log('✅ 管理员账号创建成功!');
    console.log('');
    console.log('🎯 登录信息:');
    console.log('📧 邮箱: <EMAIL>');
    console.log('🔑 密码: jocker2024');
    console.log('👤 用户名: admin');
    console.log('');
    console.log('🌐 请使用以上信息登录管理后台');

  } catch (error) {
    console.error('❌ 创建管理员账号失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createDefaultAdmin();
}

export { createDefaultAdmin };
