import { Request, Response } from 'express';
export declare const generateArticleList: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const generateContent: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const generateReview: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const testGoogleConnection: (req: Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=aiController.d.ts.map