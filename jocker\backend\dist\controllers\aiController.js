"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testGoogleConnection = exports.generateReview = exports.generateContent = exports.generateArticleList = void 0;
const aiService_1 = require("../services/aiService");
const errorHandler_1 = require("../middleware/errorHandler");
const genai_1 = require("@google/genai");
exports.generateArticleList = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const request = {
        count: parseInt(req.body.count) || 5,
        theme: req.body.theme,
    };
    const articles = await (0, aiService_1.generateArticles)(request);
    const response = {
        success: true,
        message: `成功生成 ${articles.length} 篇文章`,
        data: articles,
    };
    res.status(201).json(response);
});
exports.generateContent = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const articleId = parseInt(req.params.id);
    if (isNaN(articleId)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章 ID',
        });
    }
    const request = { articleId };
    const content = await (0, aiService_1.generateArticleContent)(request);
    const response = {
        success: true,
        message: '生成文章内容成功',
        data: { content },
    };
    res.status(200).json(response);
});
exports.generateReview = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { author, title, abstract } = req.body;
    if (!author || !title || !abstract) {
        return res.status(400).json({
            success: false,
            message: '缺少必要参数：author, title, abstract',
        });
    }
    const review = await (0, aiService_1.generatePeerReview)(author, title, abstract);
    const response = {
        success: true,
        message: '生成同行评议成功',
        data: { review },
    };
    res.status(200).json(response);
});
exports.testGoogleConnection = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const ai = new genai_1.GoogleGenAI({
            apiKey: process.env.GOOGLE_AI_API_KEY || "AIzaSyCu5MqxX5CW2ZvYtWqr9san9ZyI5cE3kLY"
        });
        const testResponse = await ai.models.generateContent({
            model: 'gemini-2.5-flash',
            contents: '请回复"连接成功"',
        });
        const response = {
            success: true,
            message: 'Google AI API 连接成功',
            data: {
                testResult: testResponse.text || '收到响应但内容为空',
                timestamp: new Date().toISOString()
            },
        };
        res.status(200).json(response);
    }
    catch (error) {
        console.error('Google AI API 连接测试失败:', error);
        const response = {
            success: false,
            message: 'Google AI API 连接失败',
            data: {
                error: error instanceof Error ? error.message : '未知错误',
                timestamp: new Date().toISOString()
            },
        };
        res.status(500).json(response);
    }
});
//# sourceMappingURL=aiController.js.map