import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../types';

/**
 * 全局错误处理中间件
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('错误详情:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    body: req.body,
    params: req.params,
    query: req.query,
  });

  // 默认错误响应
  let statusCode = 500;
  let message = '服务器内部错误';

  // 根据错误类型设置状态码和消息
  if (error.message.includes('不存在') || error.message.includes('未找到')) {
    statusCode = 404;
    message = error.message;
  } else if (
    error.message.includes('已存在') ||
    error.message.includes('已被') ||
    error.message.includes('验证失败') ||
    error.message.includes('无效')
  ) {
    statusCode = 400;
    message = error.message;
  } else if (
    error.message.includes('无权限') ||
    error.message.includes('权限不足') ||
    error.message.includes('访问被拒绝')
  ) {
    statusCode = 403;
    message = error.message;
  } else if (
    error.message.includes('认证') ||
    error.message.includes('Token') ||
    error.message.includes('登录')
  ) {
    statusCode = 401;
    message = error.message;
  }

  const response: ApiResponse = {
    success: false,
    message,
    error: process.env.NODE_ENV === 'development' ? error.stack : undefined,
  };

  res.status(statusCode).json(response);
};

/**
 * 404 处理中间件
 */
export const notFoundHandler = (req: Request, res: Response) => {
  const response: ApiResponse = {
    success: false,
    message: `路由 ${req.method} ${req.path} 不存在`,
  };

  res.status(404).json(response);
};

/**
 * 异步错误包装器
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
