import React, { useState } from 'react';
import { authApi } from '../src/services/api';

interface LoginPageProps {
  onNavigateHome: () => void;
  onLoginSuccess: () => void;
}

export const LoginPage: React.FC<LoginPageProps> = ({ onNavigateHome, onLoginSuccess }) => {
  const [isRegisterMode, setIsRegisterMode] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [username, setUsername] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      if (isRegisterMode) {
        // 注册模式
        if (password !== confirmPassword) {
          setError('密码确认不匹配');
          setIsLoading(false);
          return;
        }

        if (password.length < 6) {
          setError('密码长度至少为 6 位');
          setIsLoading(false);
          return;
        }

        // 使用后端 API 进行注册
        const { token, user } = await authApi.register({ email, password, username });

        setSuccess('注册成功！您现在是普通用户，可以浏览文章。');

        // 保存认证信息（普通用户）
        localStorage.setItem('jocker_admin_token', token);
        localStorage.setItem('jocker_admin_logged_in', 'false'); // 普通用户不是管理员
        localStorage.setItem('jocker_admin_user', JSON.stringify(user));
        localStorage.setItem('jocker_user_role', user.role || 'USER');

        // 调用登录成功回调来更新状态
        onLoginSuccess();

        // 3秒后自动跳转到首页
        setTimeout(() => {
          onNavigateHome();
        }, 3000);

      } else {
        // 登录模式
        const { token, user } = await authApi.login({ email, password });

        // 检查用户是否为管理员
        const isAdmin = user.role === 'ADMIN';

        // 保存认证信息
        localStorage.setItem('jocker_admin_token', token);
        localStorage.setItem('jocker_admin_logged_in', isAdmin ? 'true' : 'false');
        localStorage.setItem('jocker_admin_user', JSON.stringify(user));
        localStorage.setItem('jocker_user_role', user.role || 'USER');

        // 所有用户登录成功都调用 onLoginSuccess 来更新状态
        onLoginSuccess();

        if (isAdmin) {
          // 管理员直接跳转，不显示成功消息
        } else {
          setSuccess('登录成功！您现在可以浏览文章。');
          setTimeout(() => {
            onNavigateHome();
          }, 2000);
        }
      }
    } catch (error) {
      console.error(isRegisterMode ? '注册失败:' : '登录失败:', error);
      setError(error instanceof Error ? error.message : (isRegisterMode ? '注册失败，请重试' : '登录失败，请重试'));
    } finally {
      setIsLoading(false);
    }
  };

  const toggleMode = () => {
    setIsRegisterMode(!isRegisterMode);
    setError('');
    setSuccess('');
    setEmail('');
    setPassword('');
    setUsername('');
    setConfirmPassword('');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      {/* 返回首页按钮 */}
      <div className="absolute top-4 left-4">
        <button
          onClick={onNavigateHome}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
        >
          ← 返回首页
        </button>
      </div>

      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Joker</h1>
          <p className="text-sm text-gray-600">The Journal of Outrageous Claims and Kooky Experiments Research</p>
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          {isRegisterMode ? '用户注册' : '用户登录'}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          {isRegisterMode
            ? '创建新账户以浏览文章和使用功能'
            : '登录您的账户（管理员可访问后台管理）'
          }
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {isRegisterMode && (
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                  用户名
                </label>
                <div className="mt-1">
                  <input
                    id="username"
                    name="username"
                    type="text"
                    autoComplete="username"
                    required
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                    placeholder="请输入用户名"
                  />
                </div>
              </div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                邮箱
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                  placeholder="请输入邮箱地址"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                密码
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete={isRegisterMode ? "new-password" : "current-password"}
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                  placeholder={isRegisterMode ? "请输入密码（至少6位）" : "请输入密码"}
                />
              </div>
            </div>

            {isRegisterMode && (
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                  确认密码
                </label>
                <div className="mt-1">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    autoComplete="new-password"
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                    placeholder="请再次输入密码"
                  />
                </div>
              </div>
            )}

            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      {isRegisterMode ? '注册失败' : '登录失败'}
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{error}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {success && (
              <div className="rounded-md bg-green-50 p-4">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">
                      成功
                    </h3>
                    <div className="mt-2 text-sm text-green-700">
                      <p>{success}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading
                  ? (isRegisterMode ? '注册中...' : '登录中...')
                  : (isRegisterMode ? '注册' : '登录')
                }
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">
                  {isRegisterMode ? '已有账户？' : '没有账户？'}
                </span>
              </div>
            </div>

            <div className="mt-6 text-center">
              <button
                type="button"
                onClick={toggleMode}
                className="text-purple-600 hover:text-purple-500 font-medium"
              >
                {isRegisterMode ? '点击登录' : '点击注册'}
              </button>
            </div>

            <div className="mt-4">
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">
                      {isRegisterMode ? '注册说明' : '登录说明'}
                    </h3>
                    <div className="mt-2 text-sm text-blue-700">
                      {isRegisterMode ? (
                        <>
                          <p>注册后您将成为普通用户，可以浏览所有文章</p>
                          <p>管理员账户可以生成和管理文章</p>
                        </>
                      ) : (
                        <>
                          <p>管理员账户可以访问文章生成功能</p>
                          <p>普通用户可以浏览所有文章内容</p>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
