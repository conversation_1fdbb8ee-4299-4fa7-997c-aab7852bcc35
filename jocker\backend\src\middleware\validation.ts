import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

/**
 * 验证中间件工厂函数
 */
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req.body);

    if (error) {
      res.status(400).json({
        success: false,
        message: '请求数据验证失败',
        error: error.details[0].message,
      });
      return;
    }

    next();
  };
};

// 用户注册验证模式
export const registerSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': '请提供有效的邮箱地址',
    'any.required': '邮箱是必填项',
  }),
  username: Joi.string().min(2).max(50).required().messages({
    'string.min': '用户名至少需要2个字符',
    'string.max': '用户名不能超过50个字符',
    'any.required': '用户名是必填项',
  }),
  password: Joi.string().min(6).required().messages({
    'string.min': '密码至少需要6个字符',
    'any.required': '密码是必填项',
  }),
  name: Joi.string().max(100).optional().messages({
    'string.max': '姓名不能超过100个字符',
  }),
});

// 用户登录验证模式
export const loginSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': '请提供有效的邮箱地址',
    'any.required': '邮箱是必填项',
  }),
  password: Joi.string().required().messages({
    'any.required': '密码是必填项',
  }),
});

// 创建文章验证模式
export const createArticleSchema = Joi.object({
  title: Joi.string().max(500).optional(), // 增加标题长度限制
  author: Joi.string().max(200).optional(), // 增加作者名长度限制
  category: Joi.string().max(100).optional(), // 增加分类长度限制
  excerpt: Joi.string().max(2000).optional(), // 大幅增加摘要长度限制
  content: Joi.string().optional(),
  imageUrl: Joi.string().optional(), // 支持 base64 图片数据或 URL
  imagePrompt: Joi.string().max(2000).optional(), // 增加图片提示词长度限制
  published: Joi.boolean().optional(),
  featured: Joi.boolean().optional(),
});

// 更新文章验证模式
export const updateArticleSchema = Joi.object({
  title: Joi.string().max(200).optional(),
  author: Joi.string().max(100).optional(),
  category: Joi.string().max(50).optional(),
  excerpt: Joi.string().max(500).optional(),
  content: Joi.string().optional(),
  imageUrl: Joi.string().uri().optional(),
  imagePrompt: Joi.string().max(1000).optional(),
  published: Joi.boolean().optional(),
  featured: Joi.boolean().optional(),
});

// AI 生成文章验证模式
export const aiGenerateArticlesSchema = Joi.object({
  count: Joi.number().integer().min(1).max(10).optional().default(5),
  theme: Joi.string().max(200).optional(),
});

// AI 生成内容验证模式
export const aiGenerateContentSchema = Joi.object({
  articleId: Joi.number().integer().positive().required(),
});
