import { Request, Response, NextFunction } from 'express';
import { verifyToken } from '../utils/jwt';
import { JWTPayload } from '../types';

// 扩展 Request 接口以包含用户信息
declare global {
  namespace Express {
    interface Request {
      user?: JWTPayload;
    }
  }
}

/**
 * JWT 认证中间件
 */
export const authenticateToken = (req: Request, res: Response, next: NextFunction): void => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  // 🔍 调试：Token验证信息
  console.log('🔑 Token验证:', {
    hasAuthHeader: !!authHeader,
    authHeaderStart: authHeader?.substring(0, 30) + '...',
    hasToken: !!token,
    tokenLength: token?.length,
    tokenStart: token?.substring(0, 20) + '...'
  });

  if (!token) {
    console.log('❌ Token验证失败：没有提供Token');
    res.status(401).json({
      success: false,
      message: '访问被拒绝，需要提供认证 Token',
    });
    return;
  }

  try {
    const decoded = verifyToken(token);
    console.log('✅ Token验证成功:', { userId: (decoded as any).id, role: (decoded as any).role });
    req.user = decoded;
    next();
  } catch (error) {
    console.log('❌ Token验证失败：Token无效', error.message);
    res.status(403).json({
      success: false,
      message: '无效的 Token',
    });
    return;
  }
};

/**
 * 可选认证中间件（Token 可选）
 */
export const optionalAuth = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (token) {
    try {
      const decoded = verifyToken(token);
      req.user = decoded;
    } catch (error) {
      // Token 无效，但不阻止请求继续
      console.warn('可选认证中 Token 无效:', error);
    }
  }

  next();
};

/**
 * 角色权限检查中间件
 */
export const requireRole = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: '需要认证',
      });
      return;
    }

    if (!roles.includes(req.user.role)) {
      res.status(403).json({
        success: false,
        message: '权限不足',
      });
      return;
    }

    next();
  };
};

/**
 * 管理员权限检查
 */
export const requireAdmin = requireRole(['ADMIN']);

/**
 * 编辑者权限检查（编辑者和管理员都可以）
 */
export const requireEditor = requireRole(['EDITOR', 'ADMIN']);
