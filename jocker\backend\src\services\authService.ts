import prisma from '../config/database';
import { hashPassword, comparePassword } from '../utils/password';
import { generateToken } from '../utils/jwt';
import { LoginRequest, RegisterRequest, AuthResponse } from '../types';

/**
 * 用户注册服务
 */
export const registerUser = async (userData: RegisterRequest): Promise<AuthResponse> => {
  const { email, username, password, name } = userData;

  // 检查邮箱是否已存在
  const existingUserByEmail = await prisma.user.findUnique({
    where: { email },
  });

  if (existingUserByEmail) {
    throw new Error('该邮箱已被注册');
  }

  // 检查用户名是否已存在
  const existingUserByUsername = await prisma.user.findUnique({
    where: { username },
  });

  if (existingUserByUsername) {
    throw new Error('该用户名已被使用');
  }

  // 加密密码
  const hashedPassword = await hashPassword(password);

  // 创建用户
  const user = await prisma.user.create({
    data: {
      email,
      username,
      password: hashedPassword,
      name,
    },
    select: {
      id: true,
      email: true,
      username: true,
      name: true,
      avatar: true,
      role: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  // 生成 JWT Token
  const token = generateToken({
    userId: user.id,
    email: user.email,
    username: user.username,
    role: user.role,
  });

  return {
    user,
    token,
  };
};

/**
 * 用户登录服务
 */
export const loginUser = async (loginData: LoginRequest): Promise<AuthResponse> => {
  const { email, password } = loginData;

  console.log(`🔐 尝试登录用户: ${email}`);

  // 查找用户
  const user = await prisma.user.findUnique({
    where: { email },
  });

  if (!user) {
    console.log(`❌ 用户不存在: ${email}`);
    throw new Error('邮箱或密码错误');
  }

  console.log(`✅ 找到用户: ${user.username}, 状态: ${user.status || 'NULL'}`);

  // 验证密码
  const isPasswordValid = await comparePassword(password, user.password);

  if (!isPasswordValid) {
    throw new Error('邮箱或密码错误');
  }

  // 检查用户状态（如果存在）
  if (user.status && user.status === 'DISABLED') {
    throw new Error('账户已被禁用，请联系管理员');
  }

  if (user.status && user.status === 'SUSPENDED') {
    throw new Error('账户已被暂停，请联系管理员');
  }

  // 更新最后登录时间
  await prisma.user.update({
    where: { id: user.id },
    data: { lastLogin: new Date() }
  });

  // 生成 JWT Token
  const token = generateToken({
    userId: user.id,
    email: user.email,
    username: user.username,
    role: user.role,
  });

  // 返回用户信息（不包含密码）
  const { password: _, ...userWithoutPassword } = user;

  return {
    user: userWithoutPassword,
    token,
  };
};

/**
 * 根据 ID 获取用户信息
 */
export const getUserById = async (userId: number) => {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      email: true,
      username: true,
      name: true,
      avatar: true,
      role: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  if (!user) {
    throw new Error('用户不存在');
  }

  return user;
};
