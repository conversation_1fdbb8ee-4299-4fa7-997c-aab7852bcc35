"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserById = exports.loginUser = exports.registerUser = void 0;
const database_1 = __importDefault(require("../config/database"));
const password_1 = require("../utils/password");
const jwt_1 = require("../utils/jwt");
const registerUser = async (userData) => {
    const { email, username, password, name } = userData;
    const existingUserByEmail = await database_1.default.user.findUnique({
        where: { email },
    });
    if (existingUserByEmail) {
        throw new Error('该邮箱已被注册');
    }
    const existingUserByUsername = await database_1.default.user.findUnique({
        where: { username },
    });
    if (existingUserByUsername) {
        throw new Error('该用户名已被使用');
    }
    const hashedPassword = await (0, password_1.hashPassword)(password);
    const user = await database_1.default.user.create({
        data: {
            email,
            username,
            password: hashedPassword,
            name,
        },
        select: {
            id: true,
            email: true,
            username: true,
            name: true,
            avatar: true,
            role: true,
            createdAt: true,
            updatedAt: true,
        },
    });
    const token = (0, jwt_1.generateToken)({
        userId: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
    });
    return {
        user,
        token,
    };
};
exports.registerUser = registerUser;
const loginUser = async (loginData) => {
    const { email, password } = loginData;
    console.log(`🔐 尝试登录用户: ${email}`);
    const user = await database_1.default.user.findUnique({
        where: { email },
    });
    if (!user) {
        console.log(`❌ 用户不存在: ${email}`);
        throw new Error('邮箱或密码错误');
    }
    console.log(`✅ 找到用户: ${user.username}, 状态: ${user.status || 'NULL'}`);
    const isPasswordValid = await (0, password_1.comparePassword)(password, user.password);
    if (!isPasswordValid) {
        throw new Error('邮箱或密码错误');
    }
    if (user.status && user.status === 'DISABLED') {
        throw new Error('账户已被禁用，请联系管理员');
    }
    if (user.status && user.status === 'SUSPENDED') {
        throw new Error('账户已被暂停，请联系管理员');
    }
    await database_1.default.user.update({
        where: { id: user.id },
        data: { lastLogin: new Date() }
    });
    const token = (0, jwt_1.generateToken)({
        userId: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
    });
    const { password: _, ...userWithoutPassword } = user;
    return {
        user: userWithoutPassword,
        token,
    };
};
exports.loginUser = loginUser;
const getUserById = async (userId) => {
    const user = await database_1.default.user.findUnique({
        where: { id: userId },
        select: {
            id: true,
            email: true,
            username: true,
            name: true,
            avatar: true,
            role: true,
            createdAt: true,
            updatedAt: true,
        },
    });
    if (!user) {
        throw new Error('用户不存在');
    }
    return user;
};
exports.getUserById = getUserById;
//# sourceMappingURL=authService.js.map