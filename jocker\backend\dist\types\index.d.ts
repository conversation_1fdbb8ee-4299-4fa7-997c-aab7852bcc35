export interface Article {
    id: number;
    title: string;
    author: string;
    category: string;
    excerpt: string;
    content?: string | null;
    imageUrl: string;
    imagePrompt?: string | null;
    published: boolean;
    featured: boolean;
    views: number;
    likes: number;
    createdAt: Date;
    updatedAt: Date;
    userId?: number | null;
}
export interface User {
    id: number;
    email: string;
    username: string;
    name?: string | null;
    avatar?: string | null;
    role: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface Tag {
    id: number;
    name: string;
    color?: string | null;
    createdAt: Date;
}
export interface LoginRequest {
    email: string;
    password: string;
}
export interface RegisterRequest {
    email: string;
    username: string;
    password: string;
    name?: string;
}
export interface AuthResponse {
    user: Omit<User, 'password'>;
    token: string;
}
export interface CreateArticleRequest {
    title?: string;
    author?: string;
    category?: string;
    excerpt?: string;
    content?: string;
    imageUrl?: string;
    imagePrompt?: string;
    published?: boolean;
    featured?: boolean;
}
export interface UpdateArticleRequest {
    title?: string;
    author?: string;
    category?: string;
    excerpt?: string;
    content?: string;
    imageUrl?: string;
    imagePrompt?: string;
    published?: boolean;
    featured?: boolean;
}
export interface AIGenerateArticlesRequest {
    count?: number;
    theme?: string;
}
export interface AIGenerateContentRequest {
    articleId: number;
}
export interface RawArticleFromAI {
    id: number;
    category: string;
    title: string;
    author: string;
    excerpt: string;
    image_prompt: string;
}
export interface AIGenerationLog {
    id: number;
    type: string;
    prompt: string;
    response?: string | null;
    success: boolean;
    errorMsg?: string | null;
    duration?: number | null;
    createdAt: Date;
}
export interface PaginationQuery {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface PaginatedResponse<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}
export interface SearchQuery extends PaginationQuery {
    q?: string;
    category?: string;
    author?: string;
    published?: boolean;
    featured?: boolean;
}
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
}
export interface JWTPayload {
    userId: number;
    email: string;
    username: string;
    role: string;
    iat?: number;
    exp?: number;
}
//# sourceMappingURL=index.d.ts.map