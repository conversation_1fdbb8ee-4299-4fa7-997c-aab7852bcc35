import { Request, Response } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { ApiResponse } from '../types';
import prisma from '../config/database';

/**
 * 获取系统日志列表
 */
export const getSystemLogs = asyncHandler(async (req: Request, res: Response) => {
  const { page = 1, limit = 20, type } = req.query;
  const skip = (Number(page) - 1) * Number(limit);

  const where = type ? { type: type as string } : {};

  const [logs, total] = await Promise.all([
    prisma.systemLog.findMany({
      where,
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            name: true,
          }
        }
      },
      orderBy: {
        date: 'desc'
      },
      skip,
      take: Number(limit),
    }),
    prisma.systemLog.count({ where })
  ]);

  // 解析changes JSON
  const logsWithParsedChanges = logs.map(log => ({
    ...log,
    changes: JSON.parse(log.changes)
  }));

  const response: ApiResponse = {
    success: true,
    message: '获取系统日志成功',
    data: {
      logs: logsWithParsedChanges,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        totalPages: Math.ceil(total / Number(limit))
      }
    },
  };

  res.status(200).json(response);
});

/**
 * 创建系统日志
 */
export const createSystemLog = asyncHandler(async (req: Request, res: Response) => {
  const { version, type, title, description, changes } = req.body;
  const userId = (req as any).user?.userId;

  if (!version || !type || !changes) {
    return res.status(400).json({
      success: false,
      message: '版本号、类型和变更列表为必填项',
    });
  }

  // 验证type是否有效
  const validTypes = ['major', 'minor', 'patch', 'hotfix'];
  if (!validTypes.includes(type)) {
    return res.status(400).json({
      success: false,
      message: '无效的日志类型',
    });
  }

  // 检查版本号是否已存在
  const existingLog = await prisma.systemLog.findFirst({
    where: { version }
  });

  if (existingLog) {
    return res.status(400).json({
      success: false,
      message: '该版本号已存在',
    });
  }

  const log = await prisma.systemLog.create({
    data: {
      version,
      type,
      title,
      description,
      changes: JSON.stringify(changes),
      createdBy: userId,
    },
    include: {
      creator: {
        select: {
          id: true,
          username: true,
          name: true,
        }
      }
    }
  });

  const response: ApiResponse = {
    success: true,
    message: '系统日志创建成功',
    data: {
      ...log,
      changes: JSON.parse(log.changes)
    },
  };

  res.status(201).json(response);
});

/**
 * 更新系统日志
 */
export const updateSystemLog = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { version, type, title, description, changes } = req.body;

  const existingLog = await prisma.systemLog.findUnique({
    where: { id }
  });

  if (!existingLog) {
    return res.status(404).json({
      success: false,
      message: '日志不存在',
    });
  }

  // 如果更新版本号，检查是否与其他日志冲突
  if (version && version !== existingLog.version) {
    const conflictLog = await prisma.systemLog.findFirst({
      where: { 
        version,
        id: { not: id }
      }
    });

    if (conflictLog) {
      return res.status(400).json({
        success: false,
        message: '该版本号已存在',
      });
    }
  }

  const updateData: any = {};
  if (version) updateData.version = version;
  if (type) updateData.type = type;
  if (title !== undefined) updateData.title = title;
  if (description !== undefined) updateData.description = description;
  if (changes) updateData.changes = JSON.stringify(changes);

  const updatedLog = await prisma.systemLog.update({
    where: { id },
    data: updateData,
    include: {
      creator: {
        select: {
          id: true,
          username: true,
          name: true,
        }
      }
    }
  });

  const response: ApiResponse = {
    success: true,
    message: '系统日志更新成功',
    data: {
      ...updatedLog,
      changes: JSON.parse(updatedLog.changes)
    },
  };

  res.status(200).json(response);
});

/**
 * 删除系统日志
 */
export const deleteSystemLog = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const existingLog = await prisma.systemLog.findUnique({
    where: { id }
  });

  if (!existingLog) {
    return res.status(404).json({
      success: false,
      message: '日志不存在',
    });
  }

  await prisma.systemLog.delete({
    where: { id }
  });

  const response: ApiResponse = {
    success: true,
    message: '系统日志删除成功',
  };

  res.status(200).json(response);
});

/**
 * 获取日志统计信息
 */
export const getLogStats = asyncHandler(async (req: Request, res: Response) => {
  const stats = await prisma.systemLog.groupBy({
    by: ['type'],
    _count: {
      type: true
    }
  });

  const totalLogs = await prisma.systemLog.count();
  const latestLog = await prisma.systemLog.findFirst({
    orderBy: { date: 'desc' },
    select: {
      version: true,
      date: true,
      type: true
    }
  });

  const response: ApiResponse = {
    success: true,
    message: '获取日志统计成功',
    data: {
      total: totalLogs,
      byType: stats.reduce((acc, stat) => {
        acc[stat.type] = stat._count.type;
        return acc;
      }, {} as Record<string, number>),
      latest: latestLog
    },
  };

  res.status(200).json(response);
});

/**
 * 批量导入历史日志（用于迁移现有日志）
 */
export const importHistoryLogs = asyncHandler(async (req: Request, res: Response) => {
  const { logs } = req.body;
  const userId = (req as any).user?.userId;

  if (!Array.isArray(logs)) {
    return res.status(400).json({
      success: false,
      message: '日志数据格式错误',
    });
  }

  const importedLogs = [];
  
  for (const logData of logs) {
    try {
      // 检查版本号是否已存在
      const existingLog = await prisma.systemLog.findFirst({
        where: { version: logData.version }
      });

      if (!existingLog) {
        const log = await prisma.systemLog.create({
          data: {
            version: logData.version,
            date: logData.date ? new Date(logData.date) : new Date(),
            type: logData.type || 'minor',
            title: logData.title,
            description: logData.description,
            changes: JSON.stringify(logData.changes || []),
            createdBy: userId,
          }
        });
        importedLogs.push(log);
      }
    } catch (error) {
      console.error(`导入日志失败: ${logData.version}`, error);
    }
  }

  const response: ApiResponse = {
    success: true,
    message: `成功导入 ${importedLogs.length} 条日志`,
    data: { imported: importedLogs.length, total: logs.length },
  };

  res.status(200).json(response);
});
