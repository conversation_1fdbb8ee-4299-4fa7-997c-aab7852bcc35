import React from 'react';
import { Article } from '../types';

interface FeaturedArticleProps {
  article: Article;
  onNavigateToArticle: (id: number) => void;
}

export const FeaturedArticle: React.FC<FeaturedArticleProps> = ({ article, onNavigateToArticle }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
      <div className="w-full">
        <button onClick={() => onNavigateToArticle(article.id)} className="block w-full text-left">
            <img className="rounded-lg shadow-lg object-cover w-full h-96 hover:opacity-90 transition-opacity" src={article.imageUrl} alt={article.title} />
        </button>
      </div>
      <div>
        <p className="text-sm font-bold uppercase tracking-wider text-purple-700">{article.category}</p>
        <h2 className="mt-2 text-3xl sm:text-4xl font-extrabold text-gray-900 leading-tight">
          <button onClick={() => onNavigateToArticle(article.id)} className="hover:text-purple-800 transition duration-150 ease-in-out text-left">
            {article.title}
          </button>
        </h2>
        <p className="mt-4 text-lg text-gray-600">
          {article.excerpt}
        </p>
        <p className="mt-3 text-sm text-gray-500 italic">{article.author}</p>
        <button onClick={() => onNavigateToArticle(article.id)} className="mt-6 inline-block text-base font-semibold text-purple-700 hover:text-purple-900">
          Read the full study &rarr;
        </button>
      </div>
    </div>
  );
};