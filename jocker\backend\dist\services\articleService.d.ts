import { Article, CreateArticleRequest, UpdateArticleRequest, SearchQuery, PaginatedResponse } from '../types';
export declare const getArticles: (query: SearchQuery) => Promise<PaginatedResponse<Article>>;
export declare const getArticleById: (id: number) => Promise<Article>;
export declare const createArticle: (articleData: CreateArticleRequest, userId?: number) => Promise<Article>;
export declare const updateArticle: (id: number, articleData: UpdateArticleRequest, userId?: number) => Promise<Article>;
export declare const deleteArticle: (id: number, userId?: number) => Promise<void>;
export declare const getTrendingArticles: (limit?: number) => Promise<Article[]>;
export declare const getFeaturedArticles: (limit?: number) => Promise<Article[]>;
export declare const likeArticle: (id: number) => Promise<Article>;
//# sourceMappingURL=articleService.d.ts.map