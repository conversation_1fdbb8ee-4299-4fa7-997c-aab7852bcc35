"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = __importDefault(require("./auth"));
const articles_1 = __importDefault(require("./articles"));
const ai_1 = __importDefault(require("./ai"));
const admin_1 = __importDefault(require("./admin"));
const audio_1 = __importDefault(require("./audio"));
const system_1 = __importDefault(require("./system"));
const logs_1 = __importDefault(require("./logs"));
const router = (0, express_1.Router)();
router.use('/auth', auth_1.default);
router.use('/articles', articles_1.default);
router.use('/ai', ai_1.default);
router.use('/admin', admin_1.default);
router.use('/audio', audio_1.default);
router.use('/system', system_1.default);
router.use('/logs', logs_1.default);
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'Jocker API 服务正常运行',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
    });
});
router.get('/', (req, res) => {
    res.json({
        success: true,
        message: '欢迎使用 Jocker API',
        version: '1.0.0',
        endpoints: {
            auth: '/api/auth',
            articles: '/api/articles',
            ai: '/api/ai',
            admin: '/api/admin',
            audio: '/api/audio',
            system: '/api/system',
            logs: '/api/logs',
            health: '/api/health',
        },
    });
});
exports.default = router;
//# sourceMappingURL=index.js.map