"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const articleController_1 = require("../controllers/articleController");
const validation_1 = require("../middleware/validation");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.get('/', auth_1.optionalAuth, articleController_1.getArticleList);
router.get('/trending', articleController_1.getTrending);
router.get('/featured', articleController_1.getFeatured);
router.get('/:id', auth_1.optionalAuth, articleController_1.getArticle);
router.post('/', (0, validation_1.validate)(validation_1.createArticleSchema), articleController_1.createNewArticle);
router.put('/:id', (0, validation_1.validate)(validation_1.updateArticleSchema), articleController_1.updateExistingArticle);
router.delete('/:id', articleController_1.deleteExistingArticle);
router.post('/:id/like', articleController_1.likeExistingArticle);
router.get('/:id/figures', articleController_1.getArticleFigures);
router.get('/:id/real-views', articleController_1.getArticleRealViews);
router.delete('/batch', auth_1.authenticateToken, auth_1.requireEditor, articleController_1.batchDeleteArticles);
router.put('/:id/views', auth_1.authenticateToken, auth_1.requireEditor, articleController_1.updateArticleViews);
exports.default = router;
//# sourceMappingURL=articles.js.map