interface ImageGenerationConfig {
    width: number;
    height: number;
    aspectRatio: string;
    style: string;
}
export declare const generateImageWithGemini: (prompt: string, config?: ImageGenerationConfig) => Promise<{
    imageUrl: string;
    revisedPrompt?: string;
}>;
export declare const generateImageWithStableDiffusion: (prompt: string) => Promise<{
    imageUrl: string;
}>;
export declare const generateAllFiguresForArticle: (articleId: number) => Promise<void>;
export declare const regenerateFigure: (figureId: string) => Promise<void>;
export {};
//# sourceMappingURL=imageGenerationService.d.ts.map