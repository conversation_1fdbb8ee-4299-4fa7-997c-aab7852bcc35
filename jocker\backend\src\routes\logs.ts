import { Router } from 'express';
import {
  getSystemLogs,
  createSystemLog,
  updateSystemLog,
  deleteSystemLog,
  getLogStats,
  importHistoryLogs
} from '../controllers/logController';
import { authenticateToken, requireAdmin } from '../middleware/auth';

const router = Router();

/**
 * @route   GET /api/logs
 * @desc    获取系统日志列表
 * @access  Public
 */
router.get('/', getSystemLogs);

/**
 * @route   GET /api/logs/stats
 * @desc    获取日志统计信息
 * @access  Public
 */
router.get('/stats', getLogStats);

/**
 * @route   POST /api/logs
 * @desc    创建系统日志
 * @access  Private (Admin only)
 */
router.post('/', authenticateToken, requireAdmin, createSystemLog);

/**
 * @route   PUT /api/logs/:id
 * @desc    更新系统日志
 * @access  Private (Admin only)
 */
router.put('/:id', authenticateToken, requireAdmin, updateSystemLog);

/**
 * @route   DELETE /api/logs/:id
 * @desc    删除系统日志
 * @access  Private (Admin only)
 */
router.delete('/:id', authenticateToken, requireAdmin, deleteSystemLog);

/**
 * @route   POST /api/logs/import
 * @desc    批量导入历史日志
 * @access  Private (Admin only)
 */
router.post('/import', authenticateToken, requireAdmin, importHistoryLogs);

export default router;
