{"name": "jocker-backend", "version": "1.0.0", "description": "Jocker 讽刺科学期刊后端API", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "keywords": ["jocker", "api", "satirical", "science"], "author": "Jocker Team", "license": "MIT", "dependencies": {"@google/genai": "^1.8.0", "@prisma/client": "^5.22.0", "express": "^4.19.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.13.3", "express-rate-limit": "^7.4.1", "compression": "^1.7.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "dotenv": "^16.4.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.7.5", "typescript": "^5.6.3", "nodemon": "^3.1.7", "ts-node": "^10.9.2", "prisma": "^5.22.0"}}