
import React from 'react';

const SkeletonBox = ({ className }: { className?: string }) => (
  <div className={`bg-gray-200 rounded animate-pulse ${className}`} />
);

export const LoadingSkeleton: React.FC = () => {
  return (
    <div>
      <div className="text-center mb-12">
        <SkeletonBox className="h-10 w-3/4 mx-auto" />
        <SkeletonBox className="h-6 w-1/2 mx-auto mt-4" />
      </div>

      <div className="border-t border-b border-gray-200 py-8 mb-12">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <SkeletonBox className="w-full h-96 rounded-lg" />
          <div>
            <SkeletonBox className="h-4 w-1/4" />
            <SkeletonBox className="h-10 w-full mt-4" />
            <SkeletonBox className="h-8 w-5/6 mt-2" />
            <SkeletonBox className="h-20 w-full mt-4" />
            <SkeletonBox className="h-6 w-1/3 mt-6" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
        <div className="lg:col-span-2">
          <SkeletonBox className="h-8 w-1/3 mb-6" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex flex-col rounded-lg shadow-lg overflow-hidden">
                <SkeletonBox className="h-48 w-full" />
                <div className="p-6 bg-white">
                  <SkeletonBox className="h-4 w-1/4" />
                  <SkeletonBox className="h-6 w-full mt-4" />
                  <SkeletonBox className="h-16 w-full mt-3" />
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="lg:col-span-1">
          <div className="space-y-10">
            <div>
              <SkeletonBox className="h-6 w-1/2 mb-4" />
              <ul className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <li key={i} className="flex items-start">
                    <SkeletonBox className="h-8 w-8 mr-4 rounded-full" />
                    <div className="flex-1">
                      <SkeletonBox className="h-5 w-full" />
                      <SkeletonBox className="h-4 w-1/2 mt-2" />
                    </div>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <SkeletonBox className="h-6 w-1/2 mb-4" />
              <SkeletonBox className="h-56 w-full rounded-lg" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
