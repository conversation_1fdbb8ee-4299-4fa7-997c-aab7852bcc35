import { Router } from 'express';
import authRoutes from './auth';
import articleRoutes from './articles';
import aiRoutes from './ai';
import adminRoutes from './admin';
import audioRoutes from './audio';
import systemRoutes from './system';
import logRoutes from './logs';

const router = Router();

// API 路由
router.use('/auth', authRoutes);
router.use('/articles', articleRoutes);
router.use('/ai', aiRoutes);
router.use('/admin', adminRoutes);
router.use('/audio', audioRoutes);
router.use('/system', systemRoutes);
router.use('/logs', logRoutes);

// 健康检查路由
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Jocker API 服务正常运行',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
});

// API 根路由
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: '欢迎使用 Jocker API',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      articles: '/api/articles',
      ai: '/api/ai',
      admin: '/api/admin',
      audio: '/api/audio',
      system: '/api/system',
      logs: '/api/logs',
      health: '/api/health',
    },
  });
});

export default router;
