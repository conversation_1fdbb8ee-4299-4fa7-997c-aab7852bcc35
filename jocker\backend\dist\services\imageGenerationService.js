"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.regenerateFigure = exports.generateAllFiguresForArticle = exports.generateImageWithStableDiffusion = exports.generateImageWithGemini = void 0;
const genai_1 = require("@google/genai");
const database_1 = __importDefault(require("../config/database"));
const figureService_1 = require("./figureService");
const DEFAULT_CONFIG = {
    width: 1024,
    height: 1024,
    aspectRatio: '1:1',
    style: 'photographic',
};
const generateImageWithGemini = async (prompt, config = DEFAULT_CONFIG) => {
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    if (!apiKey) {
        throw new Error('Google AI API Key 未配置');
    }
    try {
        const ai = new genai_1.GoogleGenAI({ apiKey });
        const academicPrompt = `${prompt}. Professional academic illustration style, clean and clear, suitable for scientific publication, high quality, detailed, professional composition.`;
        const imageResponse = await ai.models.generateImages({
            model: 'imagen-3.0-generate-002',
            prompt: academicPrompt,
            config: {
                numberOfImages: 1,
                outputMimeType: 'image/jpeg',
                aspectRatio: config.aspectRatio,
            },
        });
        const base64ImageBytes = imageResponse.generatedImages?.[0]?.image?.imageBytes || '';
        if (!base64ImageBytes) {
            throw new Error('Gemini 未返回图片数据');
        }
        const imageUrl = `data:image/jpeg;base64,${base64ImageBytes}`;
        return {
            imageUrl,
            revisedPrompt: academicPrompt,
        };
    }
    catch (error) {
        console.error('Gemini 图片生成失败:', error);
        throw error;
    }
};
exports.generateImageWithGemini = generateImageWithGemini;
const generateImageWithStableDiffusion = async (prompt) => {
    console.log('🔄 使用 Stable Diffusion 备选方案生成图片');
    try {
        const timestamp = Date.now();
        const mockImageUrl = `https://via.placeholder.com/1024x1024/f0f0f0/666666?text=Academic+Figure+${timestamp}`;
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log(`✅ Stable Diffusion 占位符图片生成完成: ${mockImageUrl}`);
        return { imageUrl: mockImageUrl };
    }
    catch (error) {
        console.error('Stable Diffusion 图片生成失败:', error);
        throw error;
    }
};
exports.generateImageWithStableDiffusion = generateImageWithStableDiffusion;
const generateAllFiguresForArticle = async (articleId) => {
    try {
        const figures = await database_1.default.figure.findMany({
            where: {
                articleId,
                status: 'pending',
            },
            orderBy: { figureNumber: 'asc' },
        });
        console.log(`🎨 开始为文章 ${articleId} 生成 ${figures.length} 个图片`);
        for (const figure of figures) {
            try {
                await (0, figureService_1.updateFigureStatus)(figure.id, 'generating');
                let imageResult;
                try {
                    imageResult = await (0, exports.generateImageWithGemini)(figure.imagePrompt);
                }
                catch (geminiError) {
                    console.warn(`Gemini 生成失败，尝试 Stable Diffusion: ${geminiError}`);
                    imageResult = await (0, exports.generateImageWithStableDiffusion)(figure.imagePrompt);
                }
                await (0, figureService_1.updateFigureStatus)(figure.id, 'completed', imageResult.imageUrl);
                await database_1.default.aIGenerationLog.create({
                    data: {
                        type: 'figure',
                        prompt: figure.imagePrompt,
                        response: imageResult.imageUrl,
                        success: true,
                        relatedId: figure.id,
                    },
                });
                console.log(`✅ 图片 ${figure.figureNumber} 生成成功: ${imageResult.imageUrl}`);
            }
            catch (error) {
                console.error(`❌ 图片 ${figure.figureNumber} 生成失败:`, error);
                const errorMsg = error instanceof Error ? error.message : '未知错误';
                await (0, figureService_1.updateFigureStatus)(figure.id, 'failed', undefined, errorMsg);
                await database_1.default.aIGenerationLog.create({
                    data: {
                        type: 'figure',
                        prompt: figure.imagePrompt,
                        success: false,
                        errorMsg,
                        relatedId: figure.id,
                    },
                });
            }
        }
        console.log(`🎉 文章 ${articleId} 的图片生成任务完成`);
    }
    catch (error) {
        console.error('批量生成图片失败:', error);
        throw error;
    }
};
exports.generateAllFiguresForArticle = generateAllFiguresForArticle;
const regenerateFigure = async (figureId) => {
    const figure = await database_1.default.figure.findUnique({
        where: { id: figureId },
    });
    if (!figure) {
        throw new Error('图片记录不存在');
    }
    try {
        await (0, figureService_1.updateFigureStatus)(figureId, 'generating');
        let imageResult;
        try {
            imageResult = await (0, exports.generateImageWithGemini)(figure.imagePrompt);
        }
        catch (geminiError) {
            imageResult = await (0, exports.generateImageWithStableDiffusion)(figure.imagePrompt);
        }
        await (0, figureService_1.updateFigureStatus)(figureId, 'completed', imageResult.imageUrl);
        console.log(`✅ 图片 ${figure.figureNumber} 重新生成成功`);
    }
    catch (error) {
        const errorMsg = error instanceof Error ? error.message : '未知错误';
        await (0, figureService_1.updateFigureStatus)(figureId, 'failed', undefined, errorMsg);
        throw error;
    }
};
exports.regenerateFigure = regenerateFigure;
//# sourceMappingURL=imageGenerationService.js.map