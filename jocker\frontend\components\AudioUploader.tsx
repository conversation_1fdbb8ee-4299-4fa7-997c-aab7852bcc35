import React, { useState, useRef } from 'react';

interface AudioUploaderProps {
  articleId: number;
  onUploadSuccess: (audioData: any) => void;
  onUploadError: (error: string) => void;
  className?: string;
}

export const AudioUploader: React.FC<AudioUploaderProps> = ({
  articleId,
  onUploadSuccess,
  onUploadError,
  className = ''
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 验证文件类型和大小
  const validateFile = (file: File): string | null => {
    // 检查文件类型
    if (!file.type.startsWith('audio/')) {
      return '请选择音频文件';
    }

    // 检查文件大小 (50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return `文件大小不能超过50MB，当前文件: ${(file.size / 1024 / 1024).toFixed(2)}MB`;
    }

    // 🔍 调试：显示文件信息
    console.log('🎵 文件验证通过:', {
      name: file.name,
      size: file.size,
      sizeInMB: (file.size / 1024 / 1024).toFixed(2) + 'MB',
      type: file.type
    });

    return null;
  };

  // 处理文件选择
  const handleFileSelect = (file: File) => {
    const error = validateFile(file);
    if (error) {
      onUploadError(error);
      return;
    }

    setSelectedFile(file);
    // 如果没有设置标题，使用文件名
    if (!title) {
      const fileName = file.name.replace(/\.[^/.]+$/, ''); // 移除扩展名
      setTitle(fileName);
    }
  };

  // 文件输入变化
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // 拖拽处理
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // 上传文件
  const handleUpload = async () => {
    if (!selectedFile) {
      onUploadError('请选择音频文件');
      return;
    }

    if (!title.trim()) {
      onUploadError('请输入音频标题');
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('audio', selectedFile);
      formData.append('title', title.trim());
      if (description.trim()) {
        formData.append('description', description.trim());
      }

      // 🔍 调试：显示上传信息
      console.log('🚀 开始上传音频:', {
        articleId,
        fileName: selectedFile.name,
        fileSize: selectedFile.size,
        title: title.trim(),
        description: description.trim()
      });

      console.log('🎵 开始上传音频:', {
        fileName: selectedFile.name,
        fileSize: `${(selectedFile.size / 1024 / 1024).toFixed(2)}MB`,
        fileType: selectedFile.type,
        title: title.trim()
      });

      const token = localStorage.getItem('jocker_admin_token');

      // 🔍 调试：检查所有Token相关信息
      const allTokens = {
        'token': localStorage.getItem('token'),
        'jocker_admin_token': localStorage.getItem('jocker_admin_token'),
        'jocker_admin_logged_in': localStorage.getItem('jocker_admin_logged_in'),
        'jocker_user_role': localStorage.getItem('jocker_user_role')
      };

      console.log('🔑 所有Token信息:', allTokens);
      console.log('🔑 当前使用的Token:', {
        hasToken: !!token,
        tokenLength: token?.length,
        tokenStart: token?.substring(0, 30) + '...',
        tokenEnd: '...' + token?.substring(token.length - 10)
      });

      const apiUrl = `${import.meta.env.VITE_API_BASE_URL || '/api'}/audio/articles/${articleId}`;
      console.log('🎵 上传URL:', apiUrl);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      console.log('🎵 服务器响应:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

      // 检查响应状态
      if (response.status === 413) {
        onUploadError('文件太大，请选择小于50MB的音频文件');
        return;
      }

      if (!response.ok) {
        // 尝试解析错误响应
        try {
          const errorResult = await response.json();
          onUploadError(errorResult.message || `上传失败 (${response.status})`);
        } catch {
          onUploadError(`上传失败 (${response.status}): ${response.statusText}`);
        }
        return;
      }

      const result = await response.json();

      if (result.success) {
        onUploadSuccess(result.data);
        // 重置表单
        setSelectedFile(null);
        setTitle('');
        setDescription('');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        onUploadError(result.message || '上传失败');
      }
    } catch (error) {
      console.error('上传音频失败:', error);
      onUploadError('上传失败，请重试');
    } finally {
      setIsUploading(false);
    }
  };

  // 取消选择
  const handleCancel = () => {
    setSelectedFile(null);
    setTitle('');
    setDescription('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">🎵 上传音频文件</h3>

      {!selectedFile ? (
        // 文件选择区域
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragActive
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <svg
            className="mx-auto h-12 w-12 text-gray-400 mb-4"
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <p className="text-lg font-medium text-gray-900 mb-2">
            拖拽音频文件到这里，或者
          </p>
          <button
            onClick={() => fileInputRef.current?.click()}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            点击选择文件
          </button>
          <p className="text-sm text-gray-500 mt-2">
            支持 MP3, WAV, M4A 等音频格式，最大 10MB（临时限制）
          </p>
          <input
            ref={fileInputRef}
            type="file"
            accept="audio/*"
            onChange={handleFileInputChange}
            className="hidden"
          />
        </div>
      ) : (
        // 文件信息和上传表单
        <div className="space-y-4">
          {/* 选中的文件信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <svg className="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z" />
              </svg>
              <div className="flex-1">
                <p className="font-medium text-gray-900">{selectedFile.name}</p>
                <p className="text-sm text-gray-500">
                  {formatFileSize(selectedFile.size)} • {selectedFile.type}
                </p>
              </div>
              <button
                onClick={handleCancel}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
                </svg>
              </button>
            </div>
          </div>

          {/* 音频信息表单 */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                音频标题 *
              </label>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="输入音频标题"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                maxLength={200}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                音频描述
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="输入音频描述（可选）"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                maxLength={500}
              />
            </div>
          </div>

          {/* 上传按钮 */}
          <div className="flex space-x-3">
            <button
              onClick={handleUpload}
              disabled={isUploading || !title.trim()}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {isUploading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  上传中...
                </div>
              ) : (
                '上传音频'
              )}
            </button>
            <button
              onClick={handleCancel}
              disabled={isUploading}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50"
            >
              取消
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
