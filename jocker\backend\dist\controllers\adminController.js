"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteUser = exports.updateUserRole = exports.updateUserStatus = exports.getAllUsers = exports.getFounderAvatar = exports.uploadFounderAvatar = exports.clearArticleFigures = exports.saveFigure = exports.regenerateFigureImage = exports.getArticleFiguresList = exports.saveArticleContent = exports.generateAndSaveArticleContent = exports.getAIApiKey = void 0;
const asyncHandler_1 = require("../utils/asyncHandler");
const database_1 = __importDefault(require("../config/database"));
const aiService_1 = require("../services/aiService");
const imageGenerationService_1 = require("../services/imageGenerationService");
const figureService_1 = require("../services/figureService");
exports.getAIApiKey = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以获取 API Key',
        });
    }
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    if (!apiKey) {
        return res.status(500).json({
            success: false,
            message: 'Google AI API Key 未配置',
        });
    }
    const response = {
        success: true,
        message: 'API Key 获取成功',
        data: {
            apiKey: apiKey,
        },
    };
    return res.status(200).json(response);
});
exports.generateAndSaveArticleContent = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以生成文章内容',
        });
    }
    const articleId = parseInt(req.params.id);
    if (isNaN(articleId)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章 ID',
        });
    }
    try {
        const article = await database_1.default.article.findUnique({
            where: { id: articleId },
        });
        if (!article) {
            return res.status(404).json({
                success: false,
                message: '文章不存在',
            });
        }
        if (article.content && article.content.trim() !== '') {
            return res.status(200).json({
                success: true,
                message: '文章内容已存在',
                data: {
                    content: article.content,
                    cached: true,
                },
            });
        }
        const generatedContent = await (0, aiService_1.generateArticleContentWithFigures)(article);
        const updatedArticle = await database_1.default.article.update({
            where: { id: articleId },
            data: {
                content: generatedContent,
                updatedAt: new Date(),
            },
        });
        (0, imageGenerationService_1.generateAllFiguresForArticle)(articleId).catch(error => {
            console.error(`文章 ${articleId} 图片生成失败:`, error);
        });
        const response = {
            success: true,
            message: '文章内容生成并保存成功，图片正在后台生成',
            data: {
                content: generatedContent,
                cached: false,
            },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('生成文章内容失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '生成文章内容失败',
        });
    }
});
exports.saveArticleContent = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const articleId = parseInt(req.params.id);
    const { content } = req.body;
    if (isNaN(articleId)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章 ID',
        });
    }
    if (!content || typeof content !== 'string') {
        return res.status(400).json({
            success: false,
            message: '文章内容不能为空',
        });
    }
    try {
        const article = await database_1.default.article.findUnique({
            where: { id: articleId },
        });
        if (!article) {
            return res.status(404).json({
                success: false,
                message: '文章不存在',
            });
        }
        await database_1.default.article.update({
            where: { id: articleId },
            data: {
                content: content,
                updatedAt: new Date(),
            },
        });
        console.log(`✅ 文章 ${articleId} 内容保存成功`);
        const response = {
            success: true,
            message: '文章内容保存成功',
            data: { success: true },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('保存文章内容失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '保存文章内容失败',
        });
    }
});
exports.getArticleFiguresList = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const articleId = parseInt(req.params.id);
    if (isNaN(articleId)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章 ID',
        });
    }
    try {
        const figures = await (0, figureService_1.getArticleFigures)(articleId);
        const response = {
            success: true,
            message: '获取图片列表成功',
            data: { figures },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('获取图片列表失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '获取图片列表失败',
        });
    }
});
exports.regenerateFigureImage = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const figureId = req.params.id;
    try {
        (0, imageGenerationService_1.regenerateFigure)(figureId).catch(error => {
            console.error(`图片 ${figureId} 重新生成失败:`, error);
        });
        const response = {
            success: true,
            message: '图片重新生成任务已启动',
            data: { figureId },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('启动图片重新生成失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '启动图片重新生成失败',
        });
    }
});
exports.saveFigure = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const { articleId, figureNumber, title, description, caption, imagePrompt, imageUrl, status } = req.body;
    try {
        if (!articleId || !figureNumber || !title) {
            return res.status(400).json({
                success: false,
                message: '缺少必需的图片信息',
            });
        }
        const article = await database_1.default.article.findUnique({
            where: { id: articleId },
        });
        if (!article) {
            return res.status(404).json({
                success: false,
                message: '文章不存在',
            });
        }
        const figure = await database_1.default.figure.upsert({
            where: {
                articleId_figureNumber: {
                    articleId,
                    figureNumber
                }
            },
            update: {
                title,
                description: description || '',
                caption: caption || `Figure ${figureNumber}`,
                imagePrompt: imagePrompt || '',
                imageUrl: imageUrl || '',
                status: status || 'completed',
                updatedAt: new Date()
            },
            create: {
                articleId,
                figureNumber,
                title,
                description: description || '',
                caption: caption || `Figure ${figureNumber}`,
                imagePrompt: imagePrompt || '',
                imageUrl: imageUrl || '',
                status: status || 'completed',
            },
        });
        console.log(`✅ 图片 ${figureNumber} 保存成功: ${figure.id}`);
        const response = {
            success: true,
            message: '图片保存成功',
            data: { figureId: figure.id },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('保存图片失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '保存图片失败',
        });
    }
});
exports.clearArticleFigures = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const { id } = req.params;
    const articleId = parseInt(id);
    if (isNaN(articleId)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章ID',
        });
    }
    try {
        const article = await database_1.default.article.findUnique({
            where: { id: articleId },
        });
        if (!article) {
            return res.status(404).json({
                success: false,
                message: '文章不存在',
            });
        }
        const deletedFigures = await database_1.default.figure.deleteMany({
            where: {
                articleId: articleId
            }
        });
        console.log(`🗑️ 已清理文章 ${articleId} 的 ${deletedFigures.count} 个图片记录`);
        const response = {
            success: true,
            message: `已清理 ${deletedFigures.count} 个图片记录`,
            data: {
                deletedCount: deletedFigures.count
            }
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('清理图片记录失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '清理图片记录失败',
        });
    }
});
exports.uploadFounderAvatar = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以上传创始人头像',
        });
    }
    const { avatarUrl, name, title, description } = req.body;
    if (!avatarUrl || typeof avatarUrl !== 'string') {
        return res.status(400).json({
            success: false,
            message: '头像数据不能为空',
        });
    }
    try {
        const founder = await database_1.default.founder.upsert({
            where: { id: 1 },
            update: {
                avatarUrl,
                name: name || 'Dr. Serious McFunnyface',
                title: title || 'Editor-in-Chief',
                description: description || 'Leading researcher in satirical science and academic absurdity.',
                updatedAt: new Date(),
            },
            create: {
                id: 1,
                avatarUrl,
                name: name || 'Dr. Serious McFunnyface',
                title: title || 'Editor-in-Chief',
                description: description || 'Leading researcher in satirical science and academic absurdity.',
            },
        });
        console.log('✅ 创始人头像上传成功');
        const response = {
            success: true,
            message: '创始人头像上传成功',
            data: { founder },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('上传创始人头像失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '上传创始人头像失败',
        });
    }
});
exports.getFounderAvatar = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    try {
        const founder = await database_1.default.founder.findFirst({
            where: { id: 1 },
        });
        const response = {
            success: true,
            message: '获取创始人信息成功',
            data: { founder },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('获取创始人信息失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '获取创始人信息失败',
        });
    }
});
exports.getAllUsers = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以查看用户列表',
        });
    }
    try {
        const users = await database_1.default.user.findMany({
            select: {
                id: true,
                email: true,
                username: true,
                name: true,
                role: true,
                status: true,
                lastLogin: true,
                createdAt: true,
                updatedAt: true,
                _count: {
                    select: {
                        articles: true
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
        const response = {
            success: true,
            message: '获取用户列表成功',
            data: { users },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('获取用户列表失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '获取用户列表失败',
        });
    }
});
exports.updateUserStatus = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const userId = parseInt(req.params.id);
    const { status } = req.body;
    if (isNaN(userId)) {
        return res.status(400).json({
            success: false,
            message: '无效的用户 ID',
        });
    }
    if (!status || !['ACTIVE', 'DISABLED', 'SUSPENDED'].includes(status)) {
        return res.status(400).json({
            success: false,
            message: '无效的用户状态',
        });
    }
    if (userId === req.user.userId && status !== 'ACTIVE') {
        return res.status(400).json({
            success: false,
            message: '不能禁用自己的账户',
        });
    }
    try {
        const user = await database_1.default.user.update({
            where: { id: userId },
            data: { status },
            select: {
                id: true,
                username: true,
                email: true,
                status: true,
            }
        });
        console.log(`✅ 用户 ${user.username} 状态更新为: ${status}`);
        const response = {
            success: true,
            message: `用户状态更新为 ${status}`,
            data: { user },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('更新用户状态失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '更新用户状态失败',
        });
    }
});
exports.updateUserRole = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const userId = parseInt(req.params.id);
    const { role } = req.body;
    if (isNaN(userId)) {
        return res.status(400).json({
            success: false,
            message: '无效的用户 ID',
        });
    }
    if (!role || !['USER', 'EDITOR', 'ADMIN'].includes(role)) {
        return res.status(400).json({
            success: false,
            message: '无效的用户角色',
        });
    }
    if (userId === req.user.userId && role !== 'ADMIN') {
        return res.status(400).json({
            success: false,
            message: '不能修改自己的管理员权限',
        });
    }
    try {
        const user = await database_1.default.user.update({
            where: { id: userId },
            data: { role },
            select: {
                id: true,
                username: true,
                email: true,
                role: true,
            }
        });
        console.log(`✅ 用户 ${user.username} 角色更新为: ${role}`);
        const response = {
            success: true,
            message: `用户角色更新为 ${role}`,
            data: { user },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('更新用户角色失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '更新用户角色失败',
        });
    }
});
exports.deleteUser = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const userId = parseInt(req.params.id);
    if (isNaN(userId)) {
        return res.status(400).json({
            success: false,
            message: '无效的用户 ID',
        });
    }
    if (userId === req.user.userId) {
        return res.status(400).json({
            success: false,
            message: '不能删除自己的账户',
        });
    }
    try {
        const user = await database_1.default.user.findUnique({
            where: { id: userId },
            select: { username: true, email: true }
        });
        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在',
            });
        }
        await database_1.default.user.delete({
            where: { id: userId }
        });
        console.log(`🗑️ 用户 ${user.username} 已被删除`);
        const response = {
            success: true,
            message: `用户 ${user.username} 已被删除`,
            data: { deletedUser: user },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('删除用户失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '删除用户失败',
        });
    }
});
//# sourceMappingURL=adminController.js.map