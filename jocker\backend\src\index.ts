import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import path from 'path';
import dotenv from 'dotenv';

import routes from './routes';
import { errorHandler, notFoundHandler } from './middleware/errorHandler';

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// 安全中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS 配置
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

// 压缩响应
app.use(compression());

// 请求日志
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// 信任代理设置（用于获取真实IP）
app.set('trust proxy', 1);

// 限流中间件
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 分钟
  max: 100, // 每个 IP 最多 100 个请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试',
  },
  standardHeaders: true,
  legacyHeaders: false,
  // 跳过失败的请求，避免X-Forwarded-For错误
  skipFailedRequests: true,
  skipSuccessfulRequests: false,
});

app.use('/api/', limiter);

// AI 生成接口特殊限流（更严格）
const aiLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 分钟
  max: 5, // 每分钟最多 5 个 AI 请求
  message: {
    success: false,
    message: 'AI 生成请求过于频繁，请稍后再试',
  },
  // 跳过失败的请求，避免X-Forwarded-For错误
  skipFailedRequests: true,
  skipSuccessfulRequests: false,
});

app.use('/api/ai/', aiLimiter);

// 解析 JSON 请求体
app.use(express.json({ limit: '60mb' })); // 增加到60MB以支持音频文件
app.use(express.urlencoded({ extended: true, limit: '60mb' }));

// 静态文件服务 - 音频文件
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// 🔍 调试中间件：记录所有音频上传请求
app.use('/api/audio', (req, res, next) => {
  console.log('🎵 音频API请求:', {
    method: req.method,
    url: req.url,
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length'),
    userAgent: req.get('User-Agent')
  });
  next();
});

// API 路由
app.use('/api', routes);

// 根路由
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '🃏 欢迎使用 Jocker 后端 API',
    version: '1.0.0',
    documentation: '/api',
  });
});

// 404 处理
app.use(notFoundHandler);

// 全局错误处理
app.use(errorHandler);

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 Jocker 后端服务器运行在端口 ${PORT}`);
  console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
  console.log(`📡 API 地址: http://localhost:${PORT}/api`);
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`📖 API 文档: http://localhost:${PORT}/api`);
    console.log(`🔍 健康检查: http://localhost:${PORT}/api/health`);
    console.log('✅ 认证已禁用，个人使用模式');
  }
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到 SIGINT 信号，正在关闭服务器...');
  process.exit(0);
});

export default app;
