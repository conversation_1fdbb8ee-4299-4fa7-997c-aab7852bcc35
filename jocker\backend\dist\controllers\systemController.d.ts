import { Request, Response } from 'express';
export declare const getSystemStats: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getStorageAnalysis: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const cleanupStorage: (req: Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=systemController.d.ts.map