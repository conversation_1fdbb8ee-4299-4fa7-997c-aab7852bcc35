"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateArticleAudio = exports.deleteArticleAudio = exports.getArticleAudio = exports.uploadArticleAudio = exports.audioUpload = void 0;
const errorHandler_1 = require("../middleware/errorHandler");
const database_1 = __importDefault(require("../config/database"));
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path_1.default.join(process.cwd(), 'uploads', 'audio');
        if (!fs_1.default.existsSync(uploadDir)) {
            fs_1.default.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const timestamp = Date.now();
        const ext = path_1.default.extname(file.originalname);
        const randomStr = Math.random().toString(36).substring(2, 8);
        cb(null, `${timestamp}_${randomStr}${ext}`);
    }
});
const fileFilter = (req, file, cb) => {
    if (file.mimetype.startsWith('audio/')) {
        cb(null, true);
    }
    else {
        cb(new Error('只允许上传音频文件'));
    }
};
exports.audioUpload = (0, multer_1.default)({
    storage,
    fileFilter,
    limits: {
        fileSize: 50 * 1024 * 1024,
        fieldSize: 50 * 1024 * 1024,
    }
});
exports.uploadArticleAudio = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const articleId = parseInt(req.params.articleId);
    const { title, description } = req.body;
    const file = req.file;
    const userId = req.user?.userId;
    console.log('🎵 音频上传请求:', {
        articleId,
        title,
        fileSize: file?.size,
        fileName: file?.originalname,
        mimeType: file?.mimetype
    });
    if (!file) {
        return res.status(400).json({
            success: false,
            message: '请选择音频文件',
        });
    }
    if (!userId) {
        return res.status(401).json({
            success: false,
            message: '需要登录',
        });
    }
    const article = await database_1.default.article.findUnique({
        where: { id: articleId }
    });
    if (!article) {
        fs_1.default.unlinkSync(file.path);
        return res.status(404).json({
            success: false,
            message: '文章不存在',
        });
    }
    const existingAudio = await database_1.default.articleAudio.findUnique({
        where: { articleId }
    });
    if (existingAudio) {
        const oldFilePath = path_1.default.join(process.cwd(), 'uploads', 'audio', path_1.default.basename(existingAudio.audioUrl));
        if (fs_1.default.existsSync(oldFilePath)) {
            fs_1.default.unlinkSync(oldFilePath);
        }
        await database_1.default.articleAudio.delete({
            where: { articleId }
        });
    }
    const audioUrl = `/uploads/audio/${file.filename}`;
    const audio = await database_1.default.articleAudio.create({
        data: {
            articleId,
            title: title || `${article.title} - 播客`,
            description,
            fileName: file.originalname,
            fileSize: file.size,
            mimeType: file.mimetype,
            audioUrl,
            uploadedBy: userId,
        },
        include: {
            uploader: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                }
            }
        }
    });
    const response = {
        success: true,
        message: '音频上传成功',
        data: audio,
    };
    res.status(201).json(response);
});
exports.getArticleAudio = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const articleId = parseInt(req.params.articleId);
    const audio = await database_1.default.articleAudio.findUnique({
        where: { articleId },
        include: {
            uploader: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                }
            }
        }
    });
    const response = {
        success: true,
        message: audio ? '获取音频信息成功' : '该文章没有音频',
        data: audio,
    };
    res.status(200).json(response);
});
exports.deleteArticleAudio = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const articleId = parseInt(req.params.articleId);
    const userId = req.user?.userId;
    if (!userId) {
        return res.status(401).json({
            success: false,
            message: '需要登录',
        });
    }
    const audio = await database_1.default.articleAudio.findUnique({
        where: { articleId }
    });
    if (!audio) {
        return res.status(404).json({
            success: false,
            message: '音频不存在',
        });
    }
    const filePath = path_1.default.join(process.cwd(), 'uploads', 'audio', path_1.default.basename(audio.audioUrl));
    if (fs_1.default.existsSync(filePath)) {
        fs_1.default.unlinkSync(filePath);
    }
    await database_1.default.articleAudio.delete({
        where: { articleId }
    });
    const response = {
        success: true,
        message: '音频删除成功',
    };
    res.status(200).json(response);
});
exports.updateArticleAudio = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const articleId = parseInt(req.params.articleId);
    const { title, description, duration } = req.body;
    const userId = req.user?.userId;
    if (!userId) {
        return res.status(401).json({
            success: false,
            message: '需要登录',
        });
    }
    const audio = await database_1.default.articleAudio.findUnique({
        where: { articleId }
    });
    if (!audio) {
        return res.status(404).json({
            success: false,
            message: '音频不存在',
        });
    }
    const updatedAudio = await database_1.default.articleAudio.update({
        where: { articleId },
        data: {
            title,
            description,
            duration,
        },
        include: {
            uploader: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                }
            }
        }
    });
    const response = {
        success: true,
        message: '音频信息更新成功',
        data: updatedAudio,
    };
    res.status(200).json(response);
});
//# sourceMappingURL=audioController.js.map