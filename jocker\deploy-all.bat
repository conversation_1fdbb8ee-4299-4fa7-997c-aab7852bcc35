@echo off
echo ========================================
echo 🚀 Jocker Full Deployment Script
echo ========================================
echo.

if not exist "frontend" (
    echo Error: frontend directory not found!
    pause
    exit /b 1
)

if not exist "backend" (
    echo Error: backend directory not found!
    pause
    exit /b 1
)

echo Starting full deployment...
echo Note: Database will be preserved, only code and schema updated
echo.

echo ========================================
echo FRONTEND DEPLOYMENT
echo ========================================
call deploy-frontend.bat
if %errorlevel% neq 0 (
    echo Frontend deployment failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo BACKEND DEPLOYMENT
echo ========================================
call deploy-backend.bat
if %errorlevel% neq 0 (
    echo Backend deployment failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo DEPLOYMENT COMPLETED!
echo ========================================
echo Frontend: http://************:3000
echo Backend API: http://************:5003
echo.
pause
