"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireEditor = exports.requireAdmin = exports.requireRole = exports.optionalAuth = exports.authenticateToken = void 0;
const jwt_1 = require("../utils/jwt");
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    console.log('🔑 Token验证:', {
        hasAuthHeader: !!authHeader,
        authHeaderStart: authHeader?.substring(0, 30) + '...',
        hasToken: !!token,
        tokenLength: token?.length,
        tokenStart: token?.substring(0, 20) + '...'
    });
    if (!token) {
        console.log('❌ Token验证失败：没有提供Token');
        res.status(401).json({
            success: false,
            message: '访问被拒绝，需要提供认证 Token',
        });
        return;
    }
    try {
        const decoded = (0, jwt_1.verifyToken)(token);
        console.log('✅ Token验证成功:', { userId: decoded.id, role: decoded.role });
        req.user = decoded;
        next();
    }
    catch (error) {
        console.log('❌ Token验证失败：Token无效', error.message);
        res.status(403).json({
            success: false,
            message: '无效的 Token',
        });
        return;
    }
};
exports.authenticateToken = authenticateToken;
const optionalAuth = (req, res, next) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    if (token) {
        try {
            const decoded = (0, jwt_1.verifyToken)(token);
            req.user = decoded;
        }
        catch (error) {
            console.warn('可选认证中 Token 无效:', error);
        }
    }
    next();
};
exports.optionalAuth = optionalAuth;
const requireRole = (roles) => {
    return (req, res, next) => {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: '需要认证',
            });
            return;
        }
        if (!roles.includes(req.user.role)) {
            res.status(403).json({
                success: false,
                message: '权限不足',
            });
            return;
        }
        next();
    };
};
exports.requireRole = requireRole;
exports.requireAdmin = (0, exports.requireRole)(['ADMIN']);
exports.requireEditor = (0, exports.requireRole)(['EDITOR', 'ADMIN']);
//# sourceMappingURL=auth.js.map