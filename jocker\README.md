# 🃏 Joker 期刊系统

一个基于AI的学术期刊生成系统，支持严肃模式和小丑模式的文章生成。

## 📁 项目结构

```
jocker/
├── backend/           # 后端服务 (Node.js + Express + Prisma)
├── frontend/          # 前端应用 (React + TypeScript + Vite)
├── quick-log.js       # 日志添加工具
├── log-v2.3.0.json   # 日志数据示例
└── README.md          # 项目说明
```

## 🚀 主要功能

- **🎭 双模式生成**：支持严肃学术文章和戏谑文章生成
- **🖼️ 图片生成**：AI生成配图和图表
- **📄 PDF导出**：学术期刊格式的PDF下载
- **🎵 音频播客**：为文章添加音频内容
- **📊 系统监控**：服务器资源监控和存储分析
- **📋 动态日志**：在线日志管理系统
- **👥 用户管理**：多角色权限控制

## 🛠️ 技术栈

### 后端
- Node.js + Express
- Prisma ORM + SQLite
- Google AI (Gemini) API
- JWT认证

### 前端
- React + TypeScript
- Vite构建工具
- Tailwind CSS
- Markdown渲染

## 📋 日志管理

使用标准化的日志添加流程：

1. 创建/修改JSON文件：`log-v版本号.json`
2. 运行命令：`node quick-log.js log-v版本号.json`

### JSON格式示例
```json
{
  "version": "v2.4.0",
  "type": "minor",
  "title": "功能标题",
  "description": "详细描述",
  "changes": [
    "🎵 变更1：具体内容",
    "🔧 变更2：具体内容"
  ]
}
```

## 🚀 部署

### 后端部署
```bash
cd backend
npm install
npm run build
pm2 start dist/index.js --name jocker-backend
```

### 前端部署
```bash
cd frontend
npm install
npm run build
# 将dist目录内容部署到Web服务器
```

## 📊 当前版本

**v2.3.0** - 系统监控与动态日志管理

- 📊 系统监控功能
- 💾 存储分析工具  
- 🧹 存储清理功能
- 📋 动态日志管理
- 🕒 实时时间戳
- 🗄️ 数据库驱动

## 📝 许可证

MIT License
