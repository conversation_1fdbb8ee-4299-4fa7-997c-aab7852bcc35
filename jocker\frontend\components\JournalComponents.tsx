import React from 'react';
import { Article } from '../types';

// Author Card Component
export const AuthorCard: React.FC<{ author: string; affiliation?: string }> = ({ 
  author, 
  affiliation = "Department of Satirical Sciences" 
}) => (
  <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
    <div className="flex items-center">
      <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
        {author.charAt(0)}
      </div>
      <div>
        <h3 className="font-semibold text-gray-900">{author}</h3>
        <p className="text-sm text-gray-600">{affiliation}</p>
        <p className="text-xs text-gray-500 mt-1">
          Corresponding Author • {author.toLowerCase().replace(/\s+/g, '.')}@joker-journal.com
        </p>
      </div>
    </div>
  </div>
);

// Abstract Component
export const AbstractSection: React.FC<{ abstract: string; keywords?: string[] }> = ({ 
  abstract, 
  keywords = [] 
}) => (
  <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
    <h2 className="text-xl font-bold text-gray-900 mb-4 font-serif">Abstract</h2>
    <p className="text-gray-700 leading-relaxed mb-6 italic">
      {abstract}
    </p>
    
    {keywords.length > 0 && (
      <div>
        <h3 className="text-sm font-semibold text-gray-900 mb-2">Keywords:</h3>
        <div className="flex flex-wrap gap-2">
          {keywords.map((keyword, index) => (
            <span 
              key={index}
              className="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full font-medium"
            >
              {keyword}
            </span>
          ))}
        </div>
      </div>
    )}
  </div>
);

// Article Metrics Component
export const ArticleMetrics: React.FC<{ 
  views?: number; 
  citations?: number; 
  downloads?: number;
  publishedDate?: string;
}> = ({ 
  views = Math.floor(Math.random() * 1000) + 100,
  citations = Math.floor(Math.random() * 50) + 5,
  downloads = Math.floor(Math.random() * 200) + 50,
  publishedDate = new Date().toLocaleDateString()
}) => (
  <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6">
    <h3 className="text-sm font-semibold text-gray-900 mb-3">Article Metrics</h3>
    <div className="grid grid-cols-2 xl:grid-cols-2 gap-3">
      <div className="text-center">
        <div className="text-xl xl:text-lg font-bold text-purple-600">{views.toLocaleString()}</div>
        <div className="text-xs text-gray-500">Views</div>
      </div>
      <div className="text-center">
        <div className="text-xl xl:text-lg font-bold text-green-600">{citations}</div>
        <div className="text-xs text-gray-500">Citations</div>
      </div>
      <div className="text-center">
        <div className="text-xl xl:text-lg font-bold text-blue-600">{downloads}</div>
        <div className="text-xs text-gray-500">Downloads</div>
      </div>
      <div className="text-center">
        <div className="text-sm xl:text-xs font-medium text-gray-700">{publishedDate}</div>
        <div className="text-xs text-gray-500">Published</div>
      </div>
    </div>
  </div>
);

// Journal Header Component
export const JournalHeader: React.FC<{
  volume?: number;
  issue?: number;
  articleNumber?: number;
  doi?: string;
}> = ({
  volume = new Date().getFullYear(),
  issue = Math.floor(Math.random() * 12) + 1,
  articleNumber,
  doi
}) => (
  <div className="text-center mb-6 pb-4 border-b border-gray-200 bg-gray-50 -mx-4 px-4 py-3 rounded-t-lg">
    <h2 className="text-lg font-semibold text-purple-700 mb-1 font-serif">
      Joker: Journal of Satirical Science
    </h2>
    <div className="text-xs text-gray-500 space-y-0.5">
      <p>Volume {volume}, Issue {issue}</p>
      {articleNumber && <p>Article {articleNumber}</p>}
      {doi && <p className="font-mono">DOI: {doi}</p>}
      <p>ISSN: 2024-JOKE (Online)</p>
      <p className="text-gray-400">Peer Reviewed</p>
    </div>
  </div>
);

// Peer Review Badge Component
export const PeerReviewBadge: React.FC = () => (
  <div className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full mb-4">
    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
    </svg>
    Peer Reviewed
  </div>
);

// Article Navigation Component
export const ArticleNavigation: React.FC<{ 
  onPrevious?: () => void;
  onNext?: () => void;
  onHome: () => void;
  hasPrevious?: boolean;
  hasNext?: boolean;
}> = ({ onPrevious, onNext, onHome, hasPrevious = false, hasNext = false }) => (
  <div className="flex justify-between items-center py-6 border-t border-gray-200 mt-8">
    <button
      onClick={hasPrevious ? onPrevious : undefined}
      disabled={!hasPrevious}
      className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${
        hasPrevious 
          ? 'text-purple-600 hover:text-purple-800 hover:bg-purple-50' 
          : 'text-gray-400 cursor-not-allowed'
      }`}
    >
      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
      </svg>
      Previous Article
    </button>

    <button
      onClick={onHome}
      className="px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 transition-colors"
    >
      Back to Journal
    </button>

    <button
      onClick={hasNext ? onNext : undefined}
      disabled={!hasNext}
      className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${
        hasNext 
          ? 'text-purple-600 hover:text-purple-800 hover:bg-purple-50' 
          : 'text-gray-400 cursor-not-allowed'
      }`}
    >
      Next Article
      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
      </svg>
    </button>
  </div>
);

// Related Articles Component
export const RelatedArticles: React.FC<{ 
  articles: Article[];
  currentArticleId: number;
  onNavigateToArticle: (id: number) => void;
}> = ({ articles, currentArticleId, onNavigateToArticle }) => {
  const relatedArticles = articles
    .filter(article => article.id !== currentArticleId)
    .slice(0, 3);

  if (relatedArticles.length === 0) return null;

  return (
    <div className="bg-gray-50 rounded-lg p-6 mt-8">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Articles</h3>
      <div className="space-y-4">
        {relatedArticles.map((article) => (
          <div 
            key={article.id}
            className="cursor-pointer hover:bg-white p-3 rounded-md transition-colors"
            onClick={() => onNavigateToArticle(article.id)}
          >
            <h4 className="font-medium text-gray-900 hover:text-purple-600 transition-colors">
              {article.title}
            </h4>
            <p className="text-sm text-gray-600 mt-1">{article.author}</p>
            <p className="text-xs text-gray-500 mt-1">{article.category}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

// Journal Footer Component
export const JournalFooter: React.FC = () => (
  <div className="mt-12 pt-8 border-t border-gray-200">
    <div className="bg-gray-50 rounded-lg p-6">
      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">About Joker</h3>
          <p className="text-gray-600 text-sm leading-relaxed">
            Joker: Journal of Satirical Science is a peer-reviewed publication dedicated to the rigorous
            examination of the absurd. Our mission is to apply the highest standards of academic inquiry
            to the most trivial aspects of human existence.
          </p>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Editorial Board</h3>
          <div className="text-sm text-gray-600 space-y-1">
            <p><strong>Editor-in-Chief:</strong> Dr. Serious McFunnyface</p>
            <p><strong>Associate Editor:</strong> Prof. Witty Researcher</p>
            <p><strong>Managing Editor:</strong> Dr. Academic Humor</p>
          </div>
        </div>
      </div>
      
      <div className="mt-6 pt-4 border-t border-gray-200 text-center">
        <p className="text-xs text-gray-500">
          © {new Date().getFullYear()} Joker: Journal of Satirical Science. All rights reserved.
          No actual science was harmed in the making of these articles.
        </p>
      </div>
    </div>
  </div>
);
