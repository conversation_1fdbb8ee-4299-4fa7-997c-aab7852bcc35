export interface FigurePlaceholder {
    figureNumber: number;
    description: string;
    placeholder: string;
}
export declare const extractFigurePlaceholders: (content: string) => FigurePlaceholder[];
export declare const generateImagePrompt: (figureDescription: string, articleTitle: string, articleCategory: string, figureNumber: number) => Promise<string>;
export declare const createFigureRecord: (articleId: number, figureNumber: number, description: string, imagePrompt: string) => Promise<string>;
export declare const processFigurePlaceholders: (articleId: number, content: string, articleTitle: string, articleCategory: string) => Promise<{
    figureIds: string[];
    processedContent: string;
}>;
export declare const getArticleFigures: (articleId: number) => Promise<{
    id: string;
    createdAt: Date;
    updatedAt: Date;
    status: string;
    title: string;
    imageUrl: string | null;
    imagePrompt: string;
    articleId: number;
    figureNumber: number;
    description: string | null;
    caption: string;
    thumbnailUrl: string | null;
    width: number | null;
    height: number | null;
    fileSize: number | null;
    mimeType: string | null;
    errorMsg: string | null;
}[]>;
export declare const updateFigureStatus: (figureId: string, status: "pending" | "generating" | "completed" | "failed", imageUrl?: string, errorMsg?: string) => Promise<{
    id: string;
    createdAt: Date;
    updatedAt: Date;
    status: string;
    title: string;
    imageUrl: string | null;
    imagePrompt: string;
    articleId: number;
    figureNumber: number;
    description: string | null;
    caption: string;
    thumbnailUrl: string | null;
    width: number | null;
    height: number | null;
    fileSize: number | null;
    mimeType: string | null;
    errorMsg: string | null;
}>;
//# sourceMappingURL=figureService.d.ts.map