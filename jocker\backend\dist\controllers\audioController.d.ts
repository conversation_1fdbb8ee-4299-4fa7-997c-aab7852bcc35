import { Request, Response } from 'express';
import multer from 'multer';
export declare const audioUpload: multer.Multer;
export declare const uploadArticleAudio: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getArticleAudio: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const deleteArticleAudio: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const updateArticleAudio: (req: Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=audioController.d.ts.map