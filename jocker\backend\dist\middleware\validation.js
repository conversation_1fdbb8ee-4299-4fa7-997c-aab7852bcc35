"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.aiGenerateContentSchema = exports.aiGenerateArticlesSchema = exports.updateArticleSchema = exports.createArticleSchema = exports.loginSchema = exports.registerSchema = exports.validate = void 0;
const joi_1 = __importDefault(require("joi"));
const validate = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.body);
        if (error) {
            res.status(400).json({
                success: false,
                message: '请求数据验证失败',
                error: error.details[0].message,
            });
            return;
        }
        next();
    };
};
exports.validate = validate;
exports.registerSchema = joi_1.default.object({
    email: joi_1.default.string().email().required().messages({
        'string.email': '请提供有效的邮箱地址',
        'any.required': '邮箱是必填项',
    }),
    username: joi_1.default.string().min(2).max(50).required().messages({
        'string.min': '用户名至少需要2个字符',
        'string.max': '用户名不能超过50个字符',
        'any.required': '用户名是必填项',
    }),
    password: joi_1.default.string().min(6).required().messages({
        'string.min': '密码至少需要6个字符',
        'any.required': '密码是必填项',
    }),
    name: joi_1.default.string().max(100).optional().messages({
        'string.max': '姓名不能超过100个字符',
    }),
});
exports.loginSchema = joi_1.default.object({
    email: joi_1.default.string().email().required().messages({
        'string.email': '请提供有效的邮箱地址',
        'any.required': '邮箱是必填项',
    }),
    password: joi_1.default.string().required().messages({
        'any.required': '密码是必填项',
    }),
});
exports.createArticleSchema = joi_1.default.object({
    title: joi_1.default.string().max(500).optional(),
    author: joi_1.default.string().max(200).optional(),
    category: joi_1.default.string().max(100).optional(),
    excerpt: joi_1.default.string().max(2000).optional(),
    content: joi_1.default.string().optional(),
    imageUrl: joi_1.default.string().optional(),
    imagePrompt: joi_1.default.string().max(2000).optional(),
    published: joi_1.default.boolean().optional(),
    featured: joi_1.default.boolean().optional(),
});
exports.updateArticleSchema = joi_1.default.object({
    title: joi_1.default.string().max(200).optional(),
    author: joi_1.default.string().max(100).optional(),
    category: joi_1.default.string().max(50).optional(),
    excerpt: joi_1.default.string().max(500).optional(),
    content: joi_1.default.string().optional(),
    imageUrl: joi_1.default.string().uri().optional(),
    imagePrompt: joi_1.default.string().max(1000).optional(),
    published: joi_1.default.boolean().optional(),
    featured: joi_1.default.boolean().optional(),
});
exports.aiGenerateArticlesSchema = joi_1.default.object({
    count: joi_1.default.number().integer().min(1).max(10).optional().default(5),
    theme: joi_1.default.string().max(200).optional(),
});
exports.aiGenerateContentSchema = joi_1.default.object({
    articleId: joi_1.default.number().integer().positive().required(),
});
//# sourceMappingURL=validation.js.map