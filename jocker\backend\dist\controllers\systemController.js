"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupStorage = exports.getStorageAnalysis = exports.getSystemStats = void 0;
const errorHandler_1 = require("../middleware/errorHandler");
const child_process_1 = require("child_process");
const util_1 = require("util");
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const database_1 = __importDefault(require("../config/database"));
const execAsync = (0, util_1.promisify)(child_process_1.exec);
exports.getSystemStats = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { stdout: diskUsage } = await execAsync('df -h /var/www/jocker');
        const diskLines = diskUsage.split('\n');
        const diskInfo = diskLines[1].split(/\s+/);
        const { stdout: appSize } = await execAsync('du -sh /var/www/jocker/jocker');
        const totalAppSize = appSize.split('\t')[0];
        const { stdout: componentSizes } = await execAsync('du -sh /var/www/jocker/jocker/*');
        const components = componentSizes.split('\n').filter(line => line.trim()).map(line => {
            const [size, path] = line.split('\t');
            const name = path.split('/').pop();
            return { name, size };
        });
        let audioStats = { totalSize: '0B', fileCount: 0 };
        try {
            const { stdout: audioSize } = await execAsync('du -sh /var/www/jocker/jocker/backend/uploads/audio 2>/dev/null || echo "0B"');
            const { stdout: audioCount } = await execAsync('find /var/www/jocker/jocker/backend/uploads/audio -type f 2>/dev/null | wc -l || echo "0"');
            audioStats = {
                totalSize: audioSize.split('\t')[0] || '0B',
                fileCount: parseInt(audioCount.trim()) || 0
            };
        }
        catch (error) {
            console.log('音频目录不存在或为空');
        }
        const [articleCount, userCount, audioCount] = await Promise.all([
            database_1.default.article.count(),
            database_1.default.user.count(),
            database_1.default.articleAudio.count()
        ]);
        let dbSize = '0B';
        try {
            const dbPath = path_1.default.join(process.cwd(), 'prisma/dev.db');
            const stats = await promises_1.default.stat(dbPath);
            dbSize = formatBytes(stats.size);
        }
        catch (error) {
            console.log('无法获取数据库文件大小');
        }
        const { stdout: memInfo } = await execAsync('free -h');
        const memLines = memInfo.split('\n');
        const memData = memLines[1].split(/\s+/);
        const { stdout: cpuInfo } = await execAsync('top -bn1 | grep "Cpu(s)" | awk \'{print $2}\' | cut -d\'%\' -f1');
        const systemStats = {
            disk: {
                total: diskInfo[1],
                used: diskInfo[2],
                available: diskInfo[3],
                usagePercent: diskInfo[4]
            },
            application: {
                totalSize: totalAppSize,
                components: components
            },
            audio: audioStats,
            database: {
                size: dbSize,
                articles: articleCount,
                users: userCount,
                audioFiles: audioCount
            },
            memory: {
                total: memData[1],
                used: memData[2],
                free: memData[3]
            },
            cpu: {
                usage: cpuInfo.trim() + '%'
            },
            uptime: await getUptime()
        };
        const response = {
            success: true,
            message: '系统统计信息获取成功',
            data: systemStats,
        };
        res.status(200).json(response);
    }
    catch (error) {
        console.error('获取系统统计失败:', error);
        res.status(500).json({
            success: false,
            message: '获取系统统计失败',
        });
    }
});
exports.getStorageAnalysis = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const analysis = {
            audioFiles: await getAudioFileAnalysis(),
            images: await getImageAnalysis(),
            database: await getDatabaseAnalysis(),
            logs: await getLogAnalysis()
        };
        const response = {
            success: true,
            message: '存储分析获取成功',
            data: analysis,
        };
        res.status(200).json(response);
    }
    catch (error) {
        console.error('获取存储分析失败:', error);
        res.status(500).json({
            success: false,
            message: '获取存储分析失败',
        });
    }
});
exports.cleanupStorage = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const cleanupResults = [];
        try {
            await execAsync('find /var/log -name "*.log" -mtime +7 -delete 2>/dev/null || true');
            cleanupResults.push('清理了7天前的日志文件');
        }
        catch (error) {
            console.log('日志清理失败:', error);
        }
        try {
            await execAsync('npm cache clean --force 2>/dev/null || true');
            cleanupResults.push('清理了npm缓存');
        }
        catch (error) {
            console.log('npm缓存清理失败:', error);
        }
        const response = {
            success: true,
            message: '存储清理完成',
            data: { cleanupResults },
        };
        res.status(200).json(response);
    }
    catch (error) {
        console.error('存储清理失败:', error);
        res.status(500).json({
            success: false,
            message: '存储清理失败',
        });
    }
});
function formatBytes(bytes) {
    if (bytes === 0)
        return '0B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i];
}
async function getUptime() {
    try {
        const { stdout } = await execAsync('uptime -p');
        return stdout.trim();
    }
    catch (error) {
        return '未知';
    }
}
async function getAudioFileAnalysis() {
    try {
        const audioFiles = await database_1.default.articleAudio.findMany({
            select: {
                fileName: true,
                fileSize: true,
                mimeType: true,
                createdAt: true,
                article: {
                    select: {
                        title: true
                    }
                }
            },
            orderBy: {
                fileSize: 'desc'
            }
        });
        const totalSize = audioFiles.reduce((sum, file) => sum + file.fileSize, 0);
        const avgSize = audioFiles.length > 0 ? totalSize / audioFiles.length : 0;
        return {
            totalFiles: audioFiles.length,
            totalSize: formatBytes(totalSize),
            averageSize: formatBytes(avgSize),
            largestFiles: audioFiles.slice(0, 5).map(file => ({
                fileName: file.fileName,
                size: formatBytes(file.fileSize),
                articleTitle: file.article.title,
                uploadDate: file.createdAt
            }))
        };
    }
    catch (error) {
        return { totalFiles: 0, totalSize: '0B', averageSize: '0B', largestFiles: [] };
    }
}
async function getImageAnalysis() {
    try {
        const { stdout } = await execAsync('find /var/www/jocker/jocker -name "*.jpg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp" | wc -l');
        const imageCount = parseInt(stdout.trim()) || 0;
        const { stdout: imageSize } = await execAsync('find /var/www/jocker/jocker -name "*.jpg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp" -exec du -ch {} + | tail -1 | cut -f1');
        return {
            totalFiles: imageCount,
            totalSize: imageSize.trim() || '0B'
        };
    }
    catch (error) {
        return { totalFiles: 0, totalSize: '0B' };
    }
}
async function getDatabaseAnalysis() {
    try {
        const dbPath = path_1.default.join(process.cwd(), 'prisma/dev.db');
        const stats = await promises_1.default.stat(dbPath);
        const tableStats = await Promise.all([
            database_1.default.article.count(),
            database_1.default.user.count(),
            database_1.default.articleAudio.count(),
            database_1.default.figure.count()
        ]);
        return {
            fileSize: formatBytes(stats.size),
            tables: {
                articles: tableStats[0],
                users: tableStats[1],
                audioFiles: tableStats[2],
                figures: tableStats[3]
            }
        };
    }
    catch (error) {
        return { fileSize: '0B', tables: {} };
    }
}
async function getLogAnalysis() {
    try {
        const { stdout } = await execAsync('du -sh /var/log 2>/dev/null || echo "0B"');
        return {
            totalSize: stdout.split('\t')[0] || '0B'
        };
    }
    catch (error) {
        return { totalSize: '0B' };
    }
}
//# sourceMappingURL=systemController.js.map