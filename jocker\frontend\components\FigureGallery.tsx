import React, { useState } from 'react';
import { Figure } from './FigureComponents';

interface FigureGalleryProps {
  figures: Figure[];
  showAdmin?: boolean;
  onRegenerate?: (figureId: string) => void;
  compact?: boolean; // 紧凑模式，用于侧边栏
}

/**
 * 专业的图片画廊组件
 * 类似 Science/Nature 期刊的图片展示风格
 */
export const FigureGallery: React.FC<FigureGalleryProps> = ({
  figures,
  showAdmin = false,
  onRegenerate,
  compact = false
}) => {
  const [selectedFigure, setSelectedFigure] = useState<Figure | null>(null);

  if (figures.length === 0) {
    return null;
  }

  const handleFigureClick = (figure: Figure) => {
    if (figure.imageUrl) {
      setSelectedFigure(figure);
    }
  };

  const handleCloseModal = () => {
    setSelectedFigure(null);
  };

  return (
    <>
      {/* 图片网格 */}
      <div className={`grid gap-4 mb-8 ${
        compact
          ? 'grid-cols-2 gap-3'
          : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
      }`}>
        {figures.map((figure) => (
          <FigureCard
            key={figure.id}
            figure={figure}
            onClick={() => handleFigureClick(figure)}
            showAdmin={showAdmin}
            onRegenerate={onRegenerate}
            compact={compact}
          />
        ))}
      </div>

      {/* 全屏预览模态框 */}
      {selectedFigure && (
        <FigureModal
          figure={selectedFigure}
          onClose={handleCloseModal}
        />
      )}
    </>
  );
};

/**
 * 单个图片卡片组件
 */
interface FigureCardProps {
  figure: Figure;
  onClick: () => void;
  showAdmin?: boolean;
  onRegenerate?: (figureId: string) => void;
  compact?: boolean;
}

const FigureCard: React.FC<FigureCardProps> = ({
  figure,
  onClick,
  showAdmin,
  onRegenerate,
  compact = false
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  const imageHeight = compact ? 'h-24' : 'h-48';

  const renderImageContent = () => {
    switch (figure.status) {
      case 'pending':
        return (
          <div className={`w-full ${imageHeight} bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center`}>
            <svg className={`${compact ? 'w-4 h-4' : 'w-8 h-8'} text-gray-400 mb-2`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span className="text-gray-500 text-xs">Pending</span>
          </div>
        );

      case 'generating':
        return (
          <div className={`w-full ${imageHeight} bg-blue-50 border border-blue-200 rounded-lg flex flex-col items-center justify-center`}>
            <div className={`animate-spin rounded-full ${compact ? 'h-4 w-4' : 'h-6 w-6'} border-b-2 border-blue-600 mb-2`}></div>
            <span className="text-blue-600 text-xs">Generating...</span>
          </div>
        );

      case 'failed':
        return (
          <div className={`w-full ${imageHeight} bg-red-50 border border-red-200 rounded-lg flex flex-col items-center justify-center`}>
            <svg className={`${compact ? 'w-4 h-4' : 'w-8 h-8'} text-red-400 mb-2`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-red-600 text-xs">Failed</span>
            {showAdmin && onRegenerate && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  if (confirm('图片生成失败，确定要重新生成吗？')) {
                    onRegenerate(figure.id);
                  }
                }}
                className="mt-2 px-3 py-1.5 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-1"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>重新生成</span>
              </button>
            )}
          </div>
        );

      case 'completed':
        if (!figure.imageUrl) {
          return (
            <div className={`w-full ${imageHeight} bg-gray-100 rounded-lg flex items-center justify-center`}>
              <span className="text-gray-500 text-xs">No image</span>
            </div>
          );
        }

        return (
          <div className="relative group cursor-pointer">
            <img
              src={figure.imageUrl}
              alt={figure.caption}
              className={`w-full ${imageHeight} object-contain bg-gray-50 rounded-lg transition-all duration-300 hover:shadow-lg ${
                imageLoaded ? 'opacity-100' : 'opacity-0'
              }`}
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
            
            {!imageLoaded && !imageError && (
              <div className="absolute inset-0 bg-gray-200 rounded-lg flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
              </div>
            )}

            {imageError && (
              <div className="absolute inset-0 bg-gray-100 rounded-lg flex flex-col items-center justify-center">
                <span className="text-gray-500 text-xs">Load failed</span>
                {showAdmin && onRegenerate && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      if (confirm('图片加载失败，确定要重新生成吗？')) {
                        onRegenerate(figure.id);
                      }
                    }}
                    className="mt-1 px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                  >
                    重新生成
                  </button>
                )}
              </div>
            )}

            {/* 悬停覆盖层 */}
            {imageLoaded && !imageError && (
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                </svg>
              </div>
            )}

            {/* 管理员操作按钮 */}
            {showAdmin && onRegenerate && imageLoaded && (
              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    if (confirm('确定要重新生成这张图片吗？')) {
                      onRegenerate(figure.id);
                    }
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg shadow-lg transition-colors duration-200 flex items-center space-x-1"
                  title="重新生成图片"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span className="text-xs font-medium">重新生成</span>
                </button>
              </div>
            )}

            {/* 生成中状态指示器 */}
            {figure.status === 'generating' && (
              <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                <div className="bg-white rounded-lg p-4 flex flex-col items-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
                  <span className="text-sm font-medium text-gray-700">重新生成中...</span>
                </div>
              </div>
            )}
          </div>
        );

      default:
        return (
          <div className={`w-full ${imageHeight} bg-gray-100 rounded-lg flex items-center justify-center`}>
            <span className="text-gray-500 text-xs">Unknown status</span>
          </div>
        );
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-300">
      {/* 图片内容 */}
      <div onClick={onClick}>
        {renderImageContent()}
      </div>
      
      {/* 图片信息 */}
      <div className={compact ? "p-2" : "p-4"}>
        <h3 className={`font-semibold text-gray-900 ${compact ? 'text-xs' : 'text-sm'} mb-1`}>
          Figure {figure.figureNumber}
        </h3>
        {!compact && (
          <p className="text-gray-600 text-xs leading-relaxed line-clamp-3">
            {figure.description || figure.title}
          </p>
        )}

        {/* 状态指示器 */}
        <div className={`flex items-center justify-between ${compact ? 'mt-1' : 'mt-3'}`}>
          <span className={`inline-flex items-center px-2 py-1 rounded-full font-medium ${
            compact ? 'text-xs' : 'text-xs'
          } ${
            figure.status === 'completed' ? 'bg-green-100 text-green-800' :
            figure.status === 'generating' ? 'bg-blue-100 text-blue-800' :
            figure.status === 'failed' ? 'bg-red-100 text-red-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {figure.status === 'completed' ? '✓ Ready' :
             figure.status === 'generating' ? '⟳ Generating' :
             figure.status === 'failed' ? '✗ Failed' :
             '○ Pending'}
          </span>
        </div>
      </div>
    </div>
  );
};

/**
 * 全屏图片预览模态框
 */
interface FigureModalProps {
  figure: Figure;
  onClose: () => void;
}

const FigureModal: React.FC<FigureModalProps> = ({ figure, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4">
      <div className="relative max-w-6xl max-h-full bg-white rounded-lg overflow-hidden">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-colors"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* 图片内容 */}
        <div className="p-6">
          {figure.imageUrl && (
            <img
              src={figure.imageUrl}
              alt={figure.caption}
              className="w-full h-auto max-h-[70vh] object-contain rounded-lg"
            />
          )}
          
          {/* 图片信息 */}
          <div className="mt-6 max-w-4xl">
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              Figure {figure.figureNumber}
            </h2>
            <p className="text-gray-700 leading-relaxed">
              {figure.caption}
            </p>
            {figure.description && figure.description !== figure.title && (
              <p className="text-gray-600 text-sm mt-2">
                {figure.description}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
