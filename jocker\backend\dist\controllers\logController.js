"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.importHistoryLogs = exports.getLogStats = exports.deleteSystemLog = exports.updateSystemLog = exports.createSystemLog = exports.getSystemLogs = void 0;
const errorHandler_1 = require("../middleware/errorHandler");
const database_1 = __importDefault(require("../config/database"));
exports.getSystemLogs = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { page = 1, limit = 20, type } = req.query;
    const skip = (Number(page) - 1) * Number(limit);
    const where = type ? { type: type } : {};
    const [logs, total] = await Promise.all([
        database_1.default.systemLog.findMany({
            where,
            include: {
                creator: {
                    select: {
                        id: true,
                        username: true,
                        name: true,
                    }
                }
            },
            orderBy: {
                date: 'desc'
            },
            skip,
            take: Number(limit),
        }),
        database_1.default.systemLog.count({ where })
    ]);
    const logsWithParsedChanges = logs.map(log => ({
        ...log,
        changes: JSON.parse(log.changes)
    }));
    const response = {
        success: true,
        message: '获取系统日志成功',
        data: {
            logs: logsWithParsedChanges,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total,
                totalPages: Math.ceil(total / Number(limit))
            }
        },
    };
    res.status(200).json(response);
});
exports.createSystemLog = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { version, type, title, description, changes } = req.body;
    const userId = req.user?.userId;
    if (!version || !type || !changes) {
        return res.status(400).json({
            success: false,
            message: '版本号、类型和变更列表为必填项',
        });
    }
    const validTypes = ['major', 'minor', 'patch', 'hotfix'];
    if (!validTypes.includes(type)) {
        return res.status(400).json({
            success: false,
            message: '无效的日志类型',
        });
    }
    const existingLog = await database_1.default.systemLog.findFirst({
        where: { version }
    });
    if (existingLog) {
        return res.status(400).json({
            success: false,
            message: '该版本号已存在',
        });
    }
    const log = await database_1.default.systemLog.create({
        data: {
            version,
            type,
            title,
            description,
            changes: JSON.stringify(changes),
            createdBy: userId,
        },
        include: {
            creator: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                }
            }
        }
    });
    const response = {
        success: true,
        message: '系统日志创建成功',
        data: {
            ...log,
            changes: JSON.parse(log.changes)
        },
    };
    res.status(201).json(response);
});
exports.updateSystemLog = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const { version, type, title, description, changes } = req.body;
    const existingLog = await database_1.default.systemLog.findUnique({
        where: { id }
    });
    if (!existingLog) {
        return res.status(404).json({
            success: false,
            message: '日志不存在',
        });
    }
    if (version && version !== existingLog.version) {
        const conflictLog = await database_1.default.systemLog.findFirst({
            where: {
                version,
                id: { not: id }
            }
        });
        if (conflictLog) {
            return res.status(400).json({
                success: false,
                message: '该版本号已存在',
            });
        }
    }
    const updateData = {};
    if (version)
        updateData.version = version;
    if (type)
        updateData.type = type;
    if (title !== undefined)
        updateData.title = title;
    if (description !== undefined)
        updateData.description = description;
    if (changes)
        updateData.changes = JSON.stringify(changes);
    const updatedLog = await database_1.default.systemLog.update({
        where: { id },
        data: updateData,
        include: {
            creator: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                }
            }
        }
    });
    const response = {
        success: true,
        message: '系统日志更新成功',
        data: {
            ...updatedLog,
            changes: JSON.parse(updatedLog.changes)
        },
    };
    res.status(200).json(response);
});
exports.deleteSystemLog = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const existingLog = await database_1.default.systemLog.findUnique({
        where: { id }
    });
    if (!existingLog) {
        return res.status(404).json({
            success: false,
            message: '日志不存在',
        });
    }
    await database_1.default.systemLog.delete({
        where: { id }
    });
    const response = {
        success: true,
        message: '系统日志删除成功',
    };
    res.status(200).json(response);
});
exports.getLogStats = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const stats = await database_1.default.systemLog.groupBy({
        by: ['type'],
        _count: {
            type: true
        }
    });
    const totalLogs = await database_1.default.systemLog.count();
    const latestLog = await database_1.default.systemLog.findFirst({
        orderBy: { date: 'desc' },
        select: {
            version: true,
            date: true,
            type: true
        }
    });
    const response = {
        success: true,
        message: '获取日志统计成功',
        data: {
            total: totalLogs,
            byType: stats.reduce((acc, stat) => {
                acc[stat.type] = stat._count.type;
                return acc;
            }, {}),
            latest: latestLog
        },
    };
    res.status(200).json(response);
});
exports.importHistoryLogs = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { logs } = req.body;
    const userId = req.user?.userId;
    if (!Array.isArray(logs)) {
        return res.status(400).json({
            success: false,
            message: '日志数据格式错误',
        });
    }
    const importedLogs = [];
    for (const logData of logs) {
        try {
            const existingLog = await database_1.default.systemLog.findFirst({
                where: { version: logData.version }
            });
            if (!existingLog) {
                const log = await database_1.default.systemLog.create({
                    data: {
                        version: logData.version,
                        date: logData.date ? new Date(logData.date) : new Date(),
                        type: logData.type || 'minor',
                        title: logData.title,
                        description: logData.description,
                        changes: JSON.stringify(logData.changes || []),
                        createdBy: userId,
                    }
                });
                importedLogs.push(log);
            }
        }
        catch (error) {
            console.error(`导入日志失败: ${logData.version}`, error);
        }
    }
    const response = {
        success: true,
        message: `成功导入 ${importedLogs.length} 条日志`,
        data: { imported: importedLogs.length, total: logs.length },
    };
    res.status(200).json(response);
});
//# sourceMappingURL=logController.js.map