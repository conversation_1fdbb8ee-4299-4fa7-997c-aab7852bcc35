"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const systemController_1 = require("../controllers/systemController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.get('/stats', auth_1.authenticateToken, auth_1.requireAdmin, systemController_1.getSystemStats);
router.get('/storage', auth_1.authenticateToken, auth_1.requireAdmin, systemController_1.getStorageAnalysis);
router.post('/cleanup', auth_1.authenticateToken, auth_1.requireAdmin, systemController_1.cleanupStorage);
exports.default = router;
//# sourceMappingURL=system.js.map