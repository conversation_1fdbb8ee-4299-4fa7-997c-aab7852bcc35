import React, { useState, useEffect } from 'react';
import { Article } from '../types';

interface ScrollingArticleListProps {
  articles: Article[];
  onArticleClick: (article: Article) => void;
  className?: string;
}

export const ScrollingArticleList: React.FC<ScrollingArticleListProps> = ({
  articles,
  onArticleClick,
  className = ''
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoScrolling, setIsAutoScrolling] = useState(true);

  // 自动滚动效果
  useEffect(() => {
    if (!isAutoScrolling || articles.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % articles.length);
    }, 5000); // 每5秒切换一次，更符合期刊阅读节奏

    return () => clearInterval(interval);
  }, [articles.length, isAutoScrolling]);

  const handlePrevious = () => {
    setIsAutoScrolling(false);
    setCurrentIndex(prev => prev === 0 ? articles.length - 1 : prev - 1);
  };

  const handleNext = () => {
    setIsAutoScrolling(false);
    setCurrentIndex(prev => (prev + 1) % articles.length);
  };

  const handleDotClick = (index: number) => {
    setIsAutoScrolling(false);
    setCurrentIndex(index);
  };

  if (articles.length === 0) {
    return (
      <div className={`bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 text-center ${className}`}>
        <div className="animate-pulse">
          <div className="w-16 h-16 bg-gray-300 rounded-full mx-auto mb-4"></div>
          <div className="h-4 bg-gray-300 rounded w-3/4 mx-auto mb-2"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>

      {/* 紧凑的封面滚动区域 */}
      <div className="relative overflow-hidden rounded-lg">
        <div
          className="flex transition-transform duration-700 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {articles.map((article, index) => (
            <div key={article.id} className="w-full flex-shrink-0">
              <div
                className="cursor-pointer group mx-auto max-w-4xl"
                onClick={() => onArticleClick(article)}
              >
                {/* 期刊封面 - 横向布局 */}
                <div className="relative h-80 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                  {/* 背景图片 */}
                  {article.imageUrl ? (
                    <div className="absolute inset-0">
                      <img
                        src={article.imageUrl}
                        alt={article.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // 如果图片加载失败，显示渐变背景
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const parent = target.parentElement;
                          if (parent) {
                            parent.className = 'absolute inset-0 bg-gradient-to-br from-blue-900 via-blue-800 to-purple-900';
                          }
                        }}
                      />
                      {/* 深色遮罩以确保文字可读性 */}
                      <div className="absolute inset-0 bg-black/40"></div>
                    </div>
                  ) : (
                    /* 备用渐变背景 */
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-blue-800 to-purple-900"></div>
                  )}

                  {/* 装饰图案 */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="absolute top-4 left-4 w-32 h-32 border border-white/20 rounded-full"></div>
                    <div className="absolute bottom-4 right-4 w-24 h-24 border border-white/20 rounded-full"></div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 border border-white/10 rounded-full"></div>
                  </div>

                  {/* 期刊头部信息 */}
                  <div className="absolute top-4 left-6 text-white/90 z-10">
                    <div className="text-xs font-bold tracking-wider">THE JOKER JOURNAL</div>
                    <div className="text-xs mt-1">Vol. 2, Issue {new Date().getMonth() + 1} • {new Date().getFullYear()}</div>
                  </div>

                  {/* DOI信息 */}
                  <div className="absolute top-4 right-6 text-white/70 text-xs z-10">
                    DOI: {article.doi || `10.1000/joker.${article.id}`}
                  </div>

                  {/* 主要内容区域 */}
                  <div className="absolute inset-0 flex items-center justify-center p-8 z-10">
                    <div className="text-center text-white max-w-2xl">
                      {/* 文章标题 */}
                      <h3 className="text-2xl md:text-3xl font-serif font-bold mb-4 leading-tight group-hover:text-blue-200 transition-colors duration-300 drop-shadow-lg">
                        {article.title}
                      </h3>

                      {/* 作者和分类 */}
                      <div className="text-white/90 mb-4 drop-shadow-md">
                        <span className="font-medium">{article.author}</span>
                        <span className="mx-2">•</span>
                        <span>{article.category}</span>
                      </div>

                      {/* 简短摘要 */}
                      <p className="text-white/70 text-sm leading-relaxed line-clamp-2">
                        {article.excerpt}
                      </p>
                    </div>
                  </div>

                  {/* 底部发表信息 */}
                  <div className="absolute bottom-4 left-6 text-white/60 text-xs">
                    Published: {new Date(article.createdAt).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    })}
                  </div>

                  {/* 阅读提示 */}
                  <div className="absolute bottom-4 right-6 text-white/60 text-xs group-hover:text-white/80 transition-colors duration-300">
                    Click to read →
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 导航控制 - 紧凑版 */}
      <div className="flex items-center justify-center mt-6 space-x-4">
        {/* 左箭头 */}
        <button
          onClick={handlePrevious}
          className="p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
          disabled={articles.length <= 1}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        {/* 指示点 */}
        <div className="flex space-x-1.5">
          {articles.map((_, index) => (
            <button
              key={index}
              onClick={() => handleDotClick(index)}
              className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-blue-600'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>

        {/* 右箭头 */}
        <button
          onClick={handleNext}
          className="p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
          disabled={articles.length <= 1}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  );
};

// CSS 类用于文本截断
const styles = `
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`;

// 将样式注入到页面
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
