#!/usr/bin/env node

/**
 * 🚀 一键日志上传工具
 * 
 * 使用方法：
 * node quick-log.js v2.4.0                    # 使用版本号（推荐）
 * node quick-log.js logs/log-v2.4.0.json     # 使用完整路径
 * 
 * 功能：
 * - 自动读取JSON日志文件
 * - 使用admin账户登录获取认证token
 * - 调用API上传日志到数据库
 * - 支持本地和生产环境
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// 配置
const CONFIG = {
  // 管理员账户信息
  ADMIN_EMAIL: '<EMAIL>',
  ADMIN_PASSWORD: 'jocker2024',
  
  // API 配置
  LOCAL_API: 'http://localhost:5003/api',
  PROD_API: 'http://************:5003/api',
  
  // 默认使用本地环境，可通过环境变量切换
  USE_PROD: process.env.NODE_ENV === 'production' || process.argv.includes('--prod')
};

/**
 * 发送HTTP请求
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const req = client.request(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

/**
 * 检查服务器连接
 */
async function checkServerConnection(apiBase) {
  console.log('🔍 检查服务器连接...');

  try {
    const response = await makeRequest(`${apiBase}/health`);

    if (response.status === 200) {
      console.log('✅ 服务器连接正常');
      return true;
    } else {
      console.log(`⚠️ 服务器响应异常: ${response.status}`);
      return false;
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ 无法连接到服务器');
    } else {
      console.log(`❌ 连接检查失败: ${error.message}`);
    }
    return false;
  }
}

/**
 * 管理员登录获取token
 */
async function adminLogin(apiBase) {
  console.log('🔐 正在登录管理员账户...');
  console.log(`   邮箱: ${CONFIG.ADMIN_EMAIL}`);

  try {
    const response = await makeRequest(`${apiBase}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: CONFIG.ADMIN_EMAIL,
        password: CONFIG.ADMIN_PASSWORD
      })
    });

    console.log(`   响应状态: ${response.status}`);
    console.log(`   响应数据: ${JSON.stringify(response.data, null, 2)}`);

    if (response.status === 200 && response.data.success) {
      console.log('✅ 管理员登录成功');
      return response.data.data.token;
    } else {
      throw new Error(`登录失败 (${response.status}): ${response.data.message || JSON.stringify(response.data)}`);
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      throw new Error(`无法连接到服务器 ${apiBase}，请确保后端服务正在运行`);
    }
    throw new Error(`登录请求失败: ${error.message}`);
  }
}

/**
 * 上传日志到数据库
 */
async function uploadLog(apiBase, token, logData) {
  console.log(`📤 正在上传日志 ${logData.version}...`);

  try {
    const response = await makeRequest(`${apiBase}/logs`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(logData)
    });

    console.log(`   响应状态: ${response.status}`);

    if (response.status === 201 && response.data.success) {
      console.log('✅ 日志上传成功');
      return response.data.data;
    } else {
      console.log(`   响应数据: ${JSON.stringify(response.data, null, 2)}`);
      throw new Error(`上传失败 (${response.status}): ${response.data.message || JSON.stringify(response.data)}`);
    }
  } catch (error) {
    throw new Error(`上传请求失败: ${error.message}`);
  }
}

/**
 * 解析文件路径
 */
function parseFilePath(input) {
  if (!input) {
    throw new Error('请提供版本号或文件路径');
  }
  
  // 如果输入是版本号格式（如 v2.4.0）
  if (input.match(/^v?\d+\.\d+\.\d+$/)) {
    const version = input.startsWith('v') ? input : `v${input}`;
    return path.join(__dirname, 'logs', `log-${version}.json`);
  }
  
  // 如果输入是相对路径
  if (!path.isAbsolute(input)) {
    return path.join(__dirname, input);
  }
  
  // 绝对路径直接返回
  return input;
}

/**
 * 读取并验证JSON文件
 */
function readLogFile(filePath) {
  console.log(`📖 正在读取日志文件: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    throw new Error(`文件不存在: ${filePath}`);
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const logData = JSON.parse(content);
    
    // 验证必需字段
    if (!logData.version || !logData.type || !logData.changes) {
      throw new Error('日志文件格式错误：缺少必需字段 (version, type, changes)');
    }
    
    // 验证type字段
    const validTypes = ['major', 'minor', 'patch', 'hotfix'];
    if (!validTypes.includes(logData.type)) {
      throw new Error(`无效的日志类型: ${logData.type}，有效类型: ${validTypes.join(', ')}`);
    }
    
    // 验证changes字段
    if (!Array.isArray(logData.changes) || logData.changes.length === 0) {
      throw new Error('changes字段必须是非空数组');
    }
    
    console.log('✅ 日志文件验证通过');
    console.log(`   版本: ${logData.version}`);
    console.log(`   类型: ${logData.type}`);
    console.log(`   标题: ${logData.title || '无'}`);
    console.log(`   变更数量: ${logData.changes.length}`);
    
    return logData;
  } catch (error) {
    if (error instanceof SyntaxError) {
      throw new Error(`JSON格式错误: ${error.message}`);
    }
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🃏 Joker 一键日志上传工具');
    console.log('================================');
    
    // 解析命令行参数
    const input = process.argv[2];
    const filePath = parseFilePath(input);
    
    // 读取日志文件
    const logData = readLogFile(filePath);
    
    // 确定API地址
    const apiBase = CONFIG.USE_PROD ? CONFIG.PROD_API : CONFIG.LOCAL_API;
    console.log(`🌐 使用API: ${apiBase}`);

    // 检查服务器连接
    const isConnected = await checkServerConnection(apiBase);
    if (!isConnected) {
      throw new Error('服务器连接失败，请检查服务器是否正在运行');
    }

    // 管理员登录
    const token = await adminLogin(apiBase);
    
    // 上传日志
    const result = await uploadLog(apiBase, token, logData);
    
    console.log('================================');
    console.log('🎉 日志上传完成！');
    console.log(`📋 日志ID: ${result.id}`);
    console.log(`📅 创建时间: ${new Date(result.createdAt).toLocaleString('zh-CN')}`);
    
  } catch (error) {
    console.error('================================');
    console.error('❌ 错误:', error.message);
    console.error('================================');
    console.error('💡 使用帮助:');
    console.error('   node quick-log.js v2.4.0                    # 使用版本号');
    console.error('   node quick-log.js logs/log-v2.4.0.json     # 使用文件路径');
    console.error('   NODE_ENV=production node quick-log.js v2.4.0  # 使用生产环境');
    console.error('   node quick-log.js v2.4.0 --prod            # 使用生产环境');
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  adminLogin,
  uploadLog,
  readLogFile,
  parseFilePath
};
