import { Request, Response, NextFunction } from 'express';

/**
 * 异步处理器包装函数
 * 用于包装异步路由处理器，自动捕获错误并传递给错误处理中间件
 */
export const asyncHandler = (fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 异步处理器的类型定义
 */
export type AsyncHandler = (req: Request, res: Response, next: NextFunction) => Promise<any>;

/**
 * 带有类型安全的异步处理器
 */
export const createAsyncHandler = <T = any>(
  handler: (req: Request, res: Response, next: NextFunction) => Promise<T>
) => {
  return asyncHandler(handler);
};

export default asyncHandler;
