"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const logController_1 = require("../controllers/logController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.get('/', logController_1.getSystemLogs);
router.get('/stats', logController_1.getLogStats);
router.post('/', auth_1.authenticateToken, auth_1.requireAdmin, logController_1.createSystemLog);
router.put('/:id', auth_1.authenticateToken, auth_1.requireAdmin, logController_1.updateSystemLog);
router.delete('/:id', auth_1.authenticateToken, auth_1.requireAdmin, logController_1.deleteSystemLog);
router.post('/import', auth_1.authenticateToken, auth_1.requireAdmin, logController_1.importHistoryLogs);
exports.default = router;
//# sourceMappingURL=logs.js.map