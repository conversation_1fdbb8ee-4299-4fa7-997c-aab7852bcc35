import React, { useState } from 'react';
import { aiApi } from '../src/services/api';

type SubmissionStatus = 'idle' | 'loading' | 'success' | 'error';

export const SubmitPage: React.FC = () => {
    const [author, setAuthor] = useState('');
    const [title, setTitle] = useState('');
    const [abstract, setAbstract] = useState('');
    const [status, setStatus] = useState<SubmissionStatus>('idle');
    const [response, setResponse] = useState('');

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setStatus('loading');
        setResponse('');

        try {
            const aiResponse = await aiApi.generateReview({
                author,
                title,
                abstract
            });

            setResponse(aiResponse.review);
            setStatus('success');

        } catch (err) {
            console.error("Failed to get peer review:", err);
            setResponse("Our peer review AI has gone on an unscheduled sabbatical. Please try resubmitting later when it's feeling more judgmental.");
            setStatus('error');
        }
    };
    
    const getBorderColor = () => {
        if (response.startsWith("DECISION: ACCEPT")) return "border-green-500";
        if (response.startsWith("DECISION: REJECT")) return "border-red-500";
        if (status === 'error') return "border-yellow-500";
        return "border-gray-300";
    }

    return (
        <div className="max-w-2xl mx-auto">
            <div className="text-center">
                <h1 className="text-4xl font-extrabold text-gray-900 font-serif">Submit to Jocker</h1>
                <p className="mt-4 text-lg text-gray-600">Have a groundbreaking, absurd, or frankly ridiculous research idea? Our distinguished (and entirely digital) panel is ready to pass judgment.</p>
            </div>

            {status !== 'success' && status !== 'error' ? (
                <form onSubmit={handleSubmit} className="mt-10 space-y-6">
                    <div>
                        <label htmlFor="author" className="block text-sm font-medium text-gray-700">Your Name (or Pseudonym)</label>
                        <input type="text" id="author" value={author} onChange={e => setAuthor(e.target.value)} required className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" />
                    </div>
                    <div>
                        <label htmlFor="title" className="block text-sm font-medium text-gray-700">Proposed Article Title</label>
                        <input type="text" id="title" value={title} onChange={e => setTitle(e.target.value)} required className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" />
                    </div>
                    <div>
                        <label htmlFor="abstract" className="block text-sm font-medium text-gray-700">Abstract</label>
                        <textarea id="abstract" value={abstract} onChange={e => setAbstract(e.target.value)} rows={5} required className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"></textarea>
                        <p className="mt-2 text-sm text-gray-500">Briefly summarize your "findings." (Max 200 words)</p>
                    </div>
                    <div>
                        <button type="submit" disabled={status === 'loading'} className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-medium text-white bg-purple-700 hover:bg-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:bg-gray-400">
                            {status === 'loading' ? 'Submitting to the Void...' : 'Submit for Peer Review'}
                        </button>
                    </div>
                </form>
            ) : (
                <div className={`mt-10 p-6 border-l-4 ${getBorderColor()} bg-gray-50 rounded-r-lg`}>
                     <h2 className="text-2xl font-bold text-gray-900 font-serif">A Response from the Editors</h2>
                     <div className="mt-4 text-gray-700 prose" dangerouslySetInnerHTML={{ __html: response.replace(/\n/g, '<br />') }} />
                     <button onClick={() => setStatus('idle')} className="mt-6 text-purple-700 hover:text-purple-900 font-semibold">
                        Submit another manuscript &rarr;
                     </button>
                </div>
            )}
        </div>
    );
};