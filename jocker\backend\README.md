# Jocker 后端 API

Jocker 讽刺科学期刊的后端 API 服务，提供用户认证、文章管理和 AI 生成功能。

## 功能特性

- 🔐 **用户认证系统** - JWT 认证，用户注册/登录
- 📝 **文章 CRUD** - 完整的文章增删改查功能
- 🤖 **AI 生成** - 使用 Google AI 生成文章和图片
- 🔍 **搜索功能** - 支持文章搜索和分页
- 📊 **数据库管理** - 使用 Prisma ORM 和 SQLite
- 🛡️ **安全防护** - 限流、CORS、数据验证
- 📈 **日志记录** - AI 生成日志和请求日志

## 技术栈

- **Node.js** + **TypeScript**
- **Express.js** - Web 框架
- **Prisma** - ORM 数据库管理
- **SQLite** - 数据库
- **JWT** - 身份认证
- **Google AI** - AI 生成服务
- **Joi** - 数据验证

## 快速开始

### 1. 安装依赖

```bash
cd backend
npm install
```

### 2. 环境配置

复制环境变量文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的环境变量。

### 3. 数据库设置

```bash
# 生成 Prisma 客户端
npm run db:generate

# 推送数据库模式
npm run db:push
```

### 4. 启动开发服务器

```bash
npm run dev
```

服务器将在 `http://localhost:3001` 启动。

## API 接口

### 认证接口

- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/profile` - 获取用户信息
- `GET /api/auth/verify` - 验证 Token

### 文章接口

- `GET /api/articles` - 获取文章列表（支持分页和搜索）
- `GET /api/articles/:id` - 获取单篇文章
- `POST /api/articles` - 创建文章（需要认证）
- `PUT /api/articles/:id` - 更新文章（需要认证）
- `DELETE /api/articles/:id` - 删除文章（需要认证）
- `GET /api/articles/trending` - 获取热门文章
- `GET /api/articles/featured` - 获取推荐文章
- `POST /api/articles/:id/like` - 点赞文章

### AI 生成接口

- `POST /api/ai/generate-articles` - AI 生成文章列表
- `POST /api/ai/generate-content/:id` - AI 生成文章内容
- `POST /api/ai/generate-review` - AI 生成同行评议

### 其他接口

- `GET /api/health` - 健康检查
- `GET /api` - API 信息

## 数据库模式

### 用户表 (users)
- id, email, username, password, name, avatar, role, createdAt, updatedAt

### 文章表 (articles)
- id, title, author, category, excerpt, content, imageUrl, imagePrompt
- published, featured, views, likes, createdAt, updatedAt, userId

### 标签表 (tags)
- id, name, color, createdAt

### AI 生成日志表 (ai_generation_logs)
- id, type, prompt, response, success, errorMsg, duration, createdAt

## 开发命令

```bash
# 开发模式
npm run dev

# 构建项目
npm run build

# 生产模式启动
npm start

# 数据库相关
npm run db:generate    # 生成 Prisma 客户端
npm run db:push        # 推送数据库模式
npm run db:migrate     # 运行数据库迁移
npm run db:studio      # 打开 Prisma Studio
```

## 环境变量

```env
# 数据库配置
DATABASE_URL="sqlite:./dev.db"

# JWT 密钥
JWT_SECRET="your-super-secret-jwt-key"

# Google AI API Key
GOOGLE_AI_API_KEY="your-google-ai-api-key"

# 服务器配置
PORT=3001
NODE_ENV=development

# CORS 配置
FRONTEND_URL="http://localhost:3000"
```

## 部署

1. 构建项目：`npm run build`
2. 设置生产环境变量
3. 运行数据库迁移：`npm run db:migrate`
4. 启动服务：`npm start`

## 许可证

MIT License
