import React from 'react';

interface ResponsiveCardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  gradient?: boolean;
  shadow?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  padding?: 'sm' | 'md' | 'lg' | 'xl';
  rounded?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  border?: boolean;
  onClick?: () => void;
  animated?: boolean;
}

export const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  children,
  className = '',
  hover = true,
  gradient = false,
  shadow = 'md',
  padding = 'md',
  rounded = 'lg',
  border = true,
  onClick,
  animated = true
}) => {
  const baseClasses = `
    bg-white relative overflow-hidden
    ${animated ? 'transition-all duration-300 ease-in-out' : ''}
    ${onClick ? 'cursor-pointer' : ''}
  `;

  const shadowClasses = {
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl',
    '2xl': 'shadow-2xl'
  };

  const hoverShadowClasses = {
    sm: 'hover:shadow-md',
    md: 'hover:shadow-lg',
    lg: 'hover:shadow-xl',
    xl: 'hover:shadow-2xl',
    '2xl': 'hover:shadow-3xl'
  };

  const paddingClasses = {
    sm: 'p-3 sm:p-4',
    md: 'p-4 sm:p-6',
    lg: 'p-6 sm:p-8',
    xl: 'p-8 sm:p-10'
  };

  const roundedClasses = {
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    '2xl': 'rounded-2xl'
  };

  return (
    <div
      onClick={onClick}
      className={`
        ${baseClasses}
        ${shadowClasses[shadow]}
        ${hover ? hoverShadowClasses[shadow] : ''}
        ${paddingClasses[padding]}
        ${roundedClasses[rounded]}
        ${border ? 'border border-gray-200' : ''}
        ${hover && animated ? 'hover:scale-105 hover:-translate-y-1' : ''}
        ${gradient ? 'bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30' : ''}
        ${className}
      `}
    >
      {gradient && (
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 pointer-events-none" />
      )}
      
      <div className="relative z-10">
        {children}
      </div>
      
      {hover && animated && (
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/0 via-blue-500/5 to-purple-500/0 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
      )}
    </div>
  );
};

// 文章卡片组件
export const ArticleCard: React.FC<{
  title: string;
  author: string;
  excerpt: string;
  category: string;
  views?: number;
  likes?: number;
  createdAt: string;
  onClick?: () => void;
  className?: string;
}> = ({
  title,
  author,
  excerpt,
  category,
  views = 0,
  likes = 0,
  createdAt,
  onClick,
  className = ''
}) => {
  return (
    <ResponsiveCard
      onClick={onClick}
      hover={true}
      gradient={true}
      shadow="md"
      className={`group ${className}`}
    >
      {/* 分类标签 */}
      <div className="flex justify-between items-start mb-4">
        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 group-hover:bg-blue-200 transition-colors duration-300">
          {category}
        </span>
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            {views}
          </span>
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            {likes}
          </span>
        </div>
      </div>

      {/* 标题 */}
      <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2">
        {title}
      </h3>

      {/* 作者 */}
      <p className="text-sm text-gray-600 mb-3 flex items-center">
        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        {author}
      </p>

      {/* 摘要 */}
      <p className="text-gray-700 text-sm leading-relaxed mb-4 line-clamp-3">
        {excerpt}
      </p>

      {/* 底部信息 */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-100">
        <span className="text-xs text-gray-500">
          {new Date(createdAt).toLocaleDateString('zh-CN')}
        </span>
        <div className="flex items-center text-blue-600 group-hover:text-blue-700 transition-colors duration-300">
          <span className="text-sm font-medium mr-1">阅读更多</span>
          <svg className="w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </div>
      </div>
    </ResponsiveCard>
  );
};

// 统计卡片组件
export const StatsCard: React.FC<{
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red';
  className?: string;
}> = ({
  title,
  value,
  icon,
  trend,
  color = 'blue',
  className = ''
}) => {
  const colorClasses = {
    blue: 'from-blue-500 to-blue-600',
    green: 'from-green-500 to-green-600',
    purple: 'from-purple-500 to-purple-600',
    orange: 'from-orange-500 to-orange-600',
    red: 'from-red-500 to-red-600'
  };

  return (
    <ResponsiveCard
      gradient={true}
      shadow="lg"
      className={`${className}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl sm:text-3xl font-bold text-gray-900">{value}</p>
          {trend && (
            <div className={`flex items-center mt-2 text-sm ${
              trend.isPositive ? 'text-green-600' : 'text-red-600'
            }`}>
              <svg className={`w-4 h-4 mr-1 ${trend.isPositive ? '' : 'rotate-180'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
              </svg>
              {Math.abs(trend.value)}%
            </div>
          )}
        </div>
        <div className={`p-3 rounded-full bg-gradient-to-r ${colorClasses[color]} text-white`}>
          {icon}
        </div>
      </div>
    </ResponsiveCard>
  );
};
