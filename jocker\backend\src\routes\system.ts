import { Router } from 'express';
import {
  getSystemStats,
  getStorageAnalysis,
  cleanupStorage
} from '../controllers/systemController';
import { authenticateToken, requireAdmin } from '../middleware/auth';

const router = Router();

/**
 * @route   GET /api/system/stats
 * @desc    获取系统资源统计
 * @access  Private (Admin only)
 */
router.get('/stats', authenticateToken, requireAdmin, getSystemStats);

/**
 * @route   GET /api/system/storage
 * @desc    获取详细存储分析
 * @access  Private (Admin only)
 */
router.get('/storage', authenticateToken, requireAdmin, getStorageAnalysis);

/**
 * @route   POST /api/system/cleanup
 * @desc    清理临时文件和缓存
 * @access  Private (Admin only)
 */
router.post('/cleanup', authenticateToken, requireAdmin, cleanupStorage);

export default router;
