import { Request, Response } from 'express';
export declare const getArticleList: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getArticle: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const createNewArticle: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const updateExistingArticle: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const deleteExistingArticle: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getArticleRealViews: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const batchDeleteArticles: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const updateArticleViews: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getTrending: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getFeatured: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const likeExistingArticle: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getArticleFigures: (req: Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=articleController.d.ts.map