import { Router } from 'express';
import {
  uploadArticleAudio,
  getArticleAudio,
  deleteArticleAudio,
  updateArticleAudio,
  audioUpload
} from '../controllers/audioController';
import { authenticateToken, requireEditor } from '../middleware/auth';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// 音频上传错误处理中间件
const handleAudioUploadError = (error: any, req: Request, res: Response, next: NextFunction) => {
  console.error('🎵 音频上传错误:', error);

  if (error.code === 'LIMIT_FILE_SIZE') {
    return res.status(413).json({
      success: false,
      message: '文件太大，请选择小于50MB的音频文件',
    });
  }

  if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    return res.status(400).json({
      success: false,
      message: '不支持的文件类型，请上传音频文件',
    });
  }

  if (error.message === '只允许上传音频文件') {
    return res.status(400).json({
      success: false,
      message: '只允许上传音频文件',
    });
  }

  return res.status(500).json({
    success: false,
    message: '上传失败，请重试',
  });
};

/**
 * @route   POST /api/audio/articles/:articleId
 * @desc    上传文章音频
 * @access  Private (Admin/Editor)
 */
router.post(
  '/articles/:articleId',
  authenticateToken,
  requireEditor,
  (req: Request, res: Response, next: NextFunction) => {
    audioUpload.single('audio')(req, res, (error: any) => {
      if (error) {
        return handleAudioUploadError(error, req, res, next);
      }
      next();
    });
  },
  uploadArticleAudio
);

/**
 * @route   GET /api/audio/articles/:articleId
 * @desc    获取文章音频信息
 * @access  Public
 */
router.get('/articles/:articleId', getArticleAudio);

/**
 * @route   PUT /api/audio/articles/:articleId
 * @desc    更新文章音频信息
 * @access  Private (Admin/Editor)
 */
router.put(
  '/articles/:articleId',
  authenticateToken,
  requireEditor,
  updateArticleAudio
);

/**
 * @route   DELETE /api/audio/articles/:articleId
 * @desc    删除文章音频
 * @access  Private (Admin/Editor)
 */
router.delete(
  '/articles/:articleId',
  authenticateToken,
  requireEditor,
  deleteArticleAudio
);

export default router;
